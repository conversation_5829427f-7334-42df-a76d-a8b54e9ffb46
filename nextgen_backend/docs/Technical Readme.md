# NextGen Backend

A Django REST Framework based backend system for managing educational consultancy services. This system provides a comprehensive API for managing educational content, team information, and consultancy services.

## Features

### 🔧 Core Framework Features
- **Django 5.2.1** with full REST framework support
- **PostgreSQL** database backend with psycopg2-binary driver
- **Swagger/ReDoc** API documentation with drf-yasg
- **CORS** enabled with configurable origins and methods
- **Jazzmin** admin interface for enhanced UI/UX

### 📝 Content Management System
- **Rich Text Editing**
  - CKEditor 5 integration with custom configuration
  - Custom color palette support
  - Image and table management
  - Multiple toolbar configurations (default and extended)
  
- **Media Management**
  - Automated upload directory structure
  - Image formats: JPEG, PNG, GIF
  - Video formats: MP4, MOV, AVI, WEBM, MKV
  - SVG support via CharField storage
  - Automatic file type validation
  - Development media serving support

### 🏢 Organization Management
- **Team Structure**
  - Hierarchical position management
  - Staff profile system with experience tracking
  - Image upload with automatic path handling
  
- **Contact Management**
  - Validated phone number format (10 digits)
  - Email validation
  - Optional registration and PAN number storage

### 🎓 Educational Services
- **University Management**
  - Ranking system
  - Program listings
  - Rich text descriptions
  - Image upload support
  
- **Destination Management**
  - Country-specific programs
  - University affiliations
  - Flag and image support
  
- **Test Preparation**
  - Module-based structure
  - Rich text support for process documentation
  - Feature management system

### 📊 Forms and Feedback
- **Testimonial System**
  - 5-level rating system
  - Location tracking
  - University affiliation
  - Image upload capability
  
- **Contact Forms**
  - Consultancy request handling
  - Education level tracking
  - Service-specific messaging
  - Automated timestamp tracking

### 🛠 Technical Integrations
- **REST Framework Configuration**
  - Permission-based access control
  - Session and Basic authentication
  - Pagination support (10 items per page)
  - Custom viewset configurations
  
- **Security Features**
  - CSRF protection
  - Password validation rules
  - Configurable allowed hosts
  - Debug mode control
  
- **Development Tools**
  - Faker integration for test data generation
  - Custom management commands
  - Development media serving
  - Comprehensive model validation

### 📡 API Features
- **Endpoint Structure**
  - RESTful architecture
  - Nested resource support
  - Bulk operations capability
  
- **Response Formats**
  - JSON by default
  - Swagger/OpenAPI specification
  - Detailed error messages
  
- **Query Capabilities**
  - Field filtering
  - Search functionality
  - Ordering support
  - Pagination controls

## Dependencies

```
asgiref==3.8.1
django==5.2.1
django-ckeditor-5==0.2.18
django-cors-headers==4.7.0
django-filter==25.1
django-jazzmin==3.0.1
django-js-asset==3.1.2
django-rest-framework==0.1.0
djangorestframework==3.16.0
pillow==11.2.1
psycopg2-binary==2.9.10
sqlparse==0.5.3
tzdata==2025.2
```


## API Endpoints

### Content Management

- `/api/blog-posts/` - Blog posts with rich text content
- `/api/about/` - Company story, mission, and vision
- `/api/popups/` - Announcement popups with image/video support
- `/api/hero/` - Hero section content management
- `/api/experience/` - Consultancy experience and achievements

### Organization

- `/api/organizations/` - Organization details and contact info
- `/api/positions/` - Team position management
- `/api/team/` - Team member profiles

### Educational Services

- `/api/universities/` - University listings with rankings
- `/api/destinations/` - Study destination information
- `/api/services/` - Consultancy service offerings
- `/api/test-preparations/` - Test preparation resources

### Testimonials

- `/api/testimonials/` - Student testimonials with ratings





### Data Entry in models where ckeditor is used

## Destination Field

```
for Heading use ```Heading 3```
for body use ```paragraph```
for point use ```bullet points```

```



## Model Features

### BlogPost

- Title and rich text body
- CKEditor 5 integration for content editing

### Hero
- Title and description for hero section
- Image upload support
- Timestamp tracking

### Experience
- Title field
- Rich text description using CKEditor 5
- Image upload support
- Timestamp tracking

### Organization

- Complete organization details
- Contact information validation
- Logo upload capability

### Team

- Full name (first, middle, last)
- Position linking
- Experience tracking
- Timestamp tracking

### Universities & Destinations

- Image upload support
- Ranking system
- Detailed descriptions
- Program listings

### TestPreparation

- Module-based structure
- Process documentation
- Feature listings

### Testimonial

- 5-star rating system
- Location tracking
- University affiliation
- Image upload support

## CORS Configuration

CORS is configured for frontend development:

- Allowed origins: localhost:3000, 127.0.0.1:3000
- Supported methods: GET, POST, PUT, PATCH, DELETE, OPTIONS
- Credentials support enabled

## Documentation

Interactive API documentation available at:

- Swagger UI: `/swagger/` - Interactive API testing
- ReDoc: `/redoc/` - Clean documentation interface
- Raw Schema: `/swagger.json/` - OpenAPI schema

## Admin Interface

Enhanced admin interface with Jazzmin:

- Modern dashboard
- Customized model views
- User-friendly interface
- File upload support

## Media Handling

- Images: Supported formats include JPEG, PNG, GIF
- Videos: Supported formats include MP4, MOV, AVI, WEBM, MKV
- SVG: Supported svg formats(not a svg file but a char)
- Storage: Media files stored in `media/` directory
- Static files served from `static/` directory

## Database Models and Data Entry

### Core Models

#### BlogPost
```python
fields:
- title: CharField(max_length=250)
- body: CKEditor5Field()
```

#### Organization
```python
fields:
- name: CharField(max_length=255)
- address: CharField(max_length=255)
- logo: ImageField(upload_to="organization/")
- contact_number: CharField(max_length=10, validator=10 digits)
- email: EmailField(max_length=255)
- pan_no: CharField(max_length=255, optional)
- reg_no: CharField(max_length=255, optional)
- description: CharField(max_length=255, optional)
```

#### Team Structure
```python
Position:
- id: AutoField(primary_key=True)
- name: CharField(max_length=255)

Team:
- id: AutoField(primary_key=True)
- first_name: CharField(max_length=255)
- middle_name: CharField(max_length=255, optional)
- last_name: CharField(max_length=255)
- image: ImageField(upload_to="team/")
- position: ForeignKey(Position)
- experience: CharField(max_length=255)
- uploaded_at: DateTimeField(auto_now_add=True)
```

#### Educational Content
```python
Universities:
- id: AutoField(primary_key=True)
- name: CharField(max_length=255)
- image: ImageField(upload_to="universities/")
- ranking: CharField(max_length=255)
- description: CKEditor5Field()

Destination:
- id: AutoField(primary_key=True)
- name: CharField(max_length=255)
- image: ImageField(upload_to="destination/")
- programs: CharField(max_length=255)
- uni: CharField(max_length=255)
- flag: CharField(max_length=10)
- description: CKEditor5Field()

TestPreparation:
- id: AutoField(primary_key=True)
- name: CharField(max_length=255)
- description: TextField()
- about: TextField()
- modules: CKEditor5Field()
- process: CKEditor5Field()
- features: CKEditor5Field()
```

#### Forms and Interactions
```python
ConsultancyForm:
- full_name: CharField(max_length=355)
- contact_number: CharField(max_length=10, validator=10 digits)
- email: EmailField(max_length=255)
- education: CharField(choices=['12', 'Diploma', 'Bachelor', 'Masters', 'Phd'])
- country: ForeignKey(Destination)
- test: ForeignKey(TestPreparation)
- message: TextField()

SendUsMessage:
- full_name: CharField(max_length=355)
- email: EmailField(max_length=255)
- contact_number: CharField(max_length=10, validator=10 digits)
- service: ForeignKey(Service)
- message: TextField()
- created_at: DateTimeField(auto_now_add=True)
```

#### Content Management
```python
About:
- story: CKEditor5Field()
- mission: CKEditor5Field()
- vision: CKEditor5Field()
- image: ImageField(upload_to="about_images/")
- uploaded_at: DateTimeField(auto_now_add=True)

Popup:
- id: AutoField(primary_key=True)
- image: ImageField(upload_to="popups/images/")
- video: FileField(upload_to="popups/videos/", validators=[validate_video_file])
- uploaded_at: DateTimeField(auto_now_add=True)

Hero:
- id: AutoField(primary_key=True)
- title: CharField(max_length=255)
- description: CharField(max_length=1000)
- image: ImageField(upload_to="hero/")
- uploaded_at: DateTimeField(auto_now_add=True)

Experience:
- id: AutoField(primary_key=True)
- title: CharField(max_length=255)
- description: CKEditor5Field()
- image: ImageField(upload_to="experience/")
- uploaded_at: DateTimeField(auto_now_add=True)
```

#### Services
```python
Service:
- id: AutoField(primary_key=True)
- name: CharField(max_length=255)
- svg: CharField(max_length=10000, optional)
- description: TextField()
- offer: CKEditor5Field()
- process: CKEditor5Field()
```

#### Testimonials
```python
Testimonial:
- name: CharField(max_length=255)
- img: ImageField(upload_to="testimonial/")
- uni: CharField(max_length=255)
- location: CharField(max_length=255)
- rating: CharField(choices=['one', 'two', 'three', 'four', 'five'])
- message: TextField()
```

### Data Entry Methods

1. **Django Admin Interface**
   - Access via `localhost:8000`
   - Model-specific features:
     ```
     BlogPost: Title and rich text editor for body
     Organization: Logo upload and contact validation
     Team: Image upload and position assignment
     Universities: Ranking and image management
     Destinations: Program and university management
     Services: SVG and rich text for offers/process
     TestPreparation: Module and feature management
     Testimonials: Rating system and image upload
     Hero: Title, description, and image management
     Experience: Title and rich text editor for consultancy experiences
     ```
   - Search and Filter capabilities:
     ```python
     Universities: Search by name, ranking
     Destinations: Search by name, programs
     Team: Search by name, position
     ConsultancyForm: Filter by education, country, test
     ```

2. **API Endpoints**
   All endpoints support standard CRUD operations with the following format:
   ```
   List/Create: GET, POST /api/{endpoint}/
   Retrieve/Update/Delete: GET, PUT, PATCH, DELETE /api/{endpoint}/{id}/
   
   Available Endpoints:
   - blog-posts/
   - organizations/
   - positions/
   - team/
   - about/
   - popups/
   - universities/
   - destinations/
   - services/
   - test-preparations/
   - testimonials/
   - send-messages/
   - consultancy-forms/
   - hero/
   - experience/
   ```

3. **Test Data Generation**
   The system includes a data generation command for testing:
   ```bash
   # Generate sample data for all models
   python manage.py generate_fake_data
   ```
   This will create:
   - 5 team positions (CEO, Manager, Consultant, etc.)
   - 1 organization profile
   - 5 team members
   - 1 about section
   - 5 universities
   - 5 destinations (USA, UK, Canada, Australia, Germany)
   - 5 services (Study Abroad, Visa Assistance, etc.)
   - 5 test preparations (IELTS, TOEFL, GRE, GMAT, SAT)
   - 10 testimonials
   - 20 consultancy forms
   - 15 contact messages
   - 10 blog posts


4. **File Management**
   - Media files are stored in `media/` directory
   - Directory structure:
     ```
     media/
     ├── organization/    # Organization logos
     ├── team/           # Team member photos
     ├── universities/   # University images
     ├── destination/    # Destination images
     ├── about_images/   # About section images
     ├── popups/
     │   ├── images/    # Popup images
     │   └── videos/    # Popup videos
     ├── testimonial/   # Testimonial images
     ├── hero/          # Hero section images
     └── experience/    # Experience section images
     ```
   - Supported formats:
     - Images: JPEG, PNG, GIF
     - Videos: MP4, MOV, AVI, WEBM, MKV
     - SVG: Stored as text in Service model

### File Upload Specifications

1. **Images**
   - Supported formats: JPEG, PNG, GIF
   - Upload directories:
     - Organization logos: `media/organization/`
     - Team photos: `media/team/`
     - University images: `media/universities/`
     - Destination images: `media/destination/`

2. **Videos**
   - Supported formats: MP4, MOV, AVI, WEBM, MKV
   - Upload directory: `media/popups/videos/`
   - Validation enforced through `validate_video_file` function

### Data Validation

1. **Contact Numbers**
   - Must be exactly 10 digits
   - Validated using RegexValidator

2. **Email Addresses**
   - Standard email format validation
   - Maximum length: 255 characters

3. **Required Fields**
   - Most name fields are required
   - Foreign key relationships must be valid
   - Images and descriptions are optional unless specified



from django.contrib import admin
from django.contrib.admin import AdminSite
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from django.utils.html import format_html
from django.urls import reverse
from .models import (
    Organization,
    Position,
    Team,
    About,
    AboutVideo,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    SendUsMessage,
    ConsultancyForm,
    Hero,
    Experience,
)

# ============================================================================
# CUSTOM ADMIN SITE WITH ORGANIZED MODEL GROUPING
# ============================================================================

class NextGenAdminSite(AdminSite):

    site_header = "NextGen Hub Admin"
    site_title = "NextGen Hub Admin Portal"
    index_title = "Welcome to NextGen Hub Administration"

    def get_app_list(self, request, app_label=None):
        """Override to organize models into logical groups."""
        app_list = super().get_app_list(request, app_label)

        # Define model grouping and display names
        model_map = {
            # Authentication & Users
            "User": {"group": "Authentication & Users", "display_name": "Users"},

            # Organization & Team
            "Organization": {"group": "Organization & Team", "display_name": "Organization"},
            "Position": {"group": "Organization & Team", "display_name": "Team Positions"},
            "Team": {"group": "Organization & Team", "display_name": "Team Members"},

            # Website Content
            "Hero": {"group": "Website Content", "display_name": "Main Section"},
            "About": {"group": "Website Content", "display_name": "About Section"},
            "Experience": {"group": "Website Content", "display_name": "Experience Section"},
            "Popup": {"group": "Website Content", "display_name": "Popup Content"},

            # Educational Services
            "Universities": {"group": "Educational Services", "display_name": "Universities"},
            "Destination": {"group": "Educational Services", "display_name": "Study Destinations"},
            "Service": {"group": "Educational Services", "display_name": "Services"},
            "TestPreparation": {"group": "Educational Services", "display_name": "Test Preparation"},

            # Customer Feedback & Inquiries
            "Testomonial": {"group": "Customer Feedback & Inquiries", "display_name": "Testimonials"},
            "SendUsMessage": {"group": "Customer Feedback & Inquiries", "display_name": "Contact Messages"},
            "ConsultancyForm": {"group": "Customer Feedback & Inquiries", "display_name": "Consultancy Forms"},
        }

        # Build group model order from model_map
        group_model_order = {}
        for model_name, meta in model_map.items():
            group = meta["group"]
            if group not in group_model_order:
                group_model_order[group] = []
            group_model_order[group].append(model_name)

        # Group models according to the model_map
        grouped_models = {}
        for app in app_list:
            for model in app["models"]:
                model_name = model["object_name"]
                if model_name in model_map:
                    group = model_map[model_name]["group"]
                    if group not in grouped_models:
                        grouped_models[group] = {
                            "name": group,
                            "app_label": group.lower().replace(" ", "_").replace("&", "and"),
                            "models": [],
                        }
                    # Use custom display name if provided
                    if "display_name" in model_map[model_name]:
                        model["name"] = model_map[model_name]["display_name"]
                    grouped_models[group]["models"].append(model)

        # Sort models in each group according to model_map order
        for group, group_dict in grouped_models.items():
            order = group_model_order.get(group, [])
            group_dict["models"].sort(
                key=lambda m: order.index(m["object_name"])
                if m["object_name"] in order
                else 999
            )

        # Define the order of groups in the admin interface
        group_order = [
            "Organization & Team",
            "Website Content",
            "Educational Services",
            "Customer Feedback & Inquiries",
            "Authentication & Users",
        ]

        # Create the final grouped app list
        grouped_app_list = [
            grouped_models[g] for g in group_order if g in grouped_models
        ]

        return grouped_app_list


# Create custom admin site instance
admin_site = NextGenAdminSite(name="nextgen_admin")
admin_site.register(User, UserAdmin)


@admin.register(Organization, site=admin_site)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ("name", "address", "email", "contact_number", "logo_preview")
    search_fields = ("name", "address", "email", "contact_number")
    list_filter = ("address",)
    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "address", "logo", "description")
        }),
        ("Contact Details", {
            "fields": ("email", "contact_number")
        }),
        ("Legal Information", {
            "fields": ("pan_no", "reg_no"),
            "classes": ("collapse",)
        }),
    )

    def logo_preview(self, obj):
        if obj.logo:
            return format_html(
                '<img src="{}" width="50" height="50" style="border-radius: 5px;" />',
                obj.logo.url
            )
        return "No Logo"
    logo_preview.short_description = "Logo"


@admin.register(Position, site=admin_site)
class PositionAdmin(admin.ModelAdmin):
    list_display = ("name", "team_count")
    search_fields = ("name",)
    ordering = ("name",)

    def team_count(self, obj):
        """Show number of team members in this position."""
        count = obj.team_set.count()
        if count > 0:
            url = reverse("admin:apps_team_changelist") + f"?position__id__exact={obj.id}"
            return format_html('<a href="{}">{} members</a>', url, count)
        return "0 members"
    team_count.short_description = "Team Members"


@admin.register(Team, site=admin_site)
class TeamAdmin(admin.ModelAdmin):
    list_display = ("full_name", "position", "experience", "image_preview", "uploaded_at")
    search_fields = ("first_name", "last_name", "position__name", "experience")
    list_filter = ("position", "uploaded_at")
    ordering = ("position", "first_name")

    fieldsets = (
        ("Personal Information", {
            "fields": ("first_name", "middle_name", "last_name", "image")
        }),
        ("Professional Details", {
            "fields": ("position", "experience")
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )
    readonly_fields = ("uploaded_at",)

    def full_name(self, obj):
        middle = f" {obj.middle_name}" if obj.middle_name else ""
        return f"{obj.first_name}{middle} {obj.last_name}"
    full_name.short_description = "Full Name"

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 50%;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Photo"



class AboutVideoInline(admin.TabularInline):
    model = AboutVideo
    extra = 1
    fields = ("video", "order", "video_preview")
    readonly_fields = ("video_preview",)
    ordering = ("order",)

    def video_preview(self, obj):
        if obj.video:
            return format_html(
                '<video width="100" height="60" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "No Video"
    video_preview.short_description = "Preview"


@admin.register(About, site=admin_site)
class AboutAdmin(admin.ModelAdmin):
    inlines = [AboutVideoInline]
    list_display = ("__str__", "image_preview", "video_count", "uploaded_at")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Content", {
            "fields": ("story", "image")
        }),
        ("Mission & Vision", {
            "fields": ("mission", "vision"),
            "description": "Add mission and vision points as JSON array"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Image"

    def video_count(self, obj):
        count = obj.videos.count()
        return f"{count} video{'s' if count != 1 else ''}"
    video_count.short_description = "Videos"


@admin.register(Hero, site=admin_site)
class HeroAdmin(admin.ModelAdmin):
    list_display = ("title", "media_preview", "uploaded_at")
    search_fields = ("title", "description")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Content", {
            "fields": ("title", "description")
        }),
        ("Media", {
            "fields": ("image", "video"),
            "description": "Add either image or video for the hero section"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def media_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="80" height="50" style="border-radius: 5px;" />',
                obj.image.url
            )
        elif obj.video:
            return format_html(
                '<video width="80" height="50" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "No Media"
    media_preview.short_description = "Media"


@admin.register(Experience, site=admin_site)
class ExperienceAdmin(admin.ModelAdmin):
    list_display = ("title", "image_preview", "experience_count", "uploaded_at")
    search_fields = ("title", "about")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Content", {
            "fields": ("title", "about", "image")
        }),
        ("Experience Points", {
            "fields": ("experience",),
            "description": "Add experience points as JSON array"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Image"

    def experience_count(self, obj):
        if obj.experience:
            count = len(obj.experience)
            return f"{count} point{'s' if count != 1 else ''}"
        return "0 points"
    experience_count.short_description = "Experience Points"


@admin.register(Popup, site=admin_site)
class PopupAdmin(admin.ModelAdmin):
    list_display = ("id", "media_type", "media_preview", "uploaded_at")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Media Content", {
            "fields": ("image", "video"),
            "description": "Add either image or video for the popup"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def media_type(self, obj):
        if obj.image and obj.video:
            return "Image & Video"
        elif obj.image:
            return "Image"
        elif obj.video:
            return "Video"
        return "No Media"
    media_type.short_description = "Media Type"

    def media_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        elif obj.video:
            return format_html(
                '<video width="60" height="40" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "No Media"
    media_preview.short_description = "Preview"


@admin.register(Universities, site=admin_site)
class UniversitiesAdmin(admin.ModelAdmin):
    list_display = ("name", "ranking", "image_preview", "programs_count", "admission_count")
    search_fields = ("name", "ranking", "about")
    list_filter = ("ranking",)
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "image", "ranking", "about")
        }),
        ("Programs & Requirements", {
            "fields": ("program_offered", "admission_required"),
            "description": "Add programs and admission requirements as JSON arrays"
        }),
        ("Financial Information", {
            "fields": ("cost", "scholarship"),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="50" height="50" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Logo"

    def programs_count(self, obj):
        if obj.program_offered:
            count = len(obj.program_offered)
            return f"{count} program{'s' if count != 1 else ''}"
        return "0 programs"
    programs_count.short_description = "Programs"

    def admission_count(self, obj):
        if obj.admission_required:
            count = len(obj.admission_required)
            return f"{count} requirement{'s' if count != 1 else ''}"
        return "0 requirements"
    admission_count.short_description = "Requirements"


@admin.register(Destination, site=admin_site)
class DestinationAdmin(admin.ModelAdmin):
    list_display = ("name", "flag", "programs", "uni_count", "image_preview")
    search_fields = ("name", "programs", "uni", "description")
    list_filter = ("flag",)
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "image", "flag", "description")
        }),
        ("Academic Details", {
            "fields": ("programs", "uni")
        }),
        ("Study Benefits", {
            "fields": ("why_study", "top_universities", "popular_courses"),
            "description": "Add study benefits, universities, and courses as JSON arrays"
        }),
        ("Cost Information", {
            "fields": ("cost_of_study",),
            "description": "Add cost information as JSON array with tuition, living expenses, etc.",
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Image"

    def uni_count(self, obj):
        """Show university count information."""
        return obj.uni if obj.uni else "Not specified"
    uni_count.short_description = "Universities"


@admin.register(Service, site=admin_site)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ("name", "description_preview", "offers_count", "process_count")
    search_fields = ("name", "description")
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "svg", "description")
        }),
        ("Service Benefits", {
            "fields": ("why_to_choose",),
            "description": "Add reasons to choose this service as JSON array"
        }),
        ("Service Offerings", {
            "fields": ("offer",),
            "description": "Add service offerings with service name and summary"
        }),
        ("Process Steps", {
            "fields": ("process",),
            "description": "Add process steps with process name and summary"
        }),
    )

    def description_preview(self, obj):
        if len(obj.description) > 50:
            return f"{obj.description[:50]}..."
        return obj.description
    description_preview.short_description = "Description"

    def offers_count(self, obj):
        if obj.offer:
            count = len(obj.offer)
            return f"{count} offer{'s' if count != 1 else ''}"
        return "0 offers"
    offers_count.short_description = "Offers"

    def process_count(self, obj):
        if obj.process:
            count = len(obj.process)
            return f"{count} step{'s' if count != 1 else ''}"
        return "0 steps"
    process_count.short_description = "Process Steps"


@admin.register(TestPreparation, site=admin_site)
class TestPreparationAdmin(admin.ModelAdmin):
    list_display = ("name", "description_preview", "modules_count", "process_count")
    search_fields = ("name", "description", "about")
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "description", "about")
        }),
        ("Course Modules", {
            "fields": ("modules",),
            "description": "Add course modules with name, summary, duration, and sections"
        }),
        ("Preparation Process", {
            "fields": ("process",),
            "description": "Add preparation process steps with name and summary"
        }),
        ("Course Features", {
            "fields": ("features",),
            "description": "Add course features with name and summary",
            "classes": ("collapse",)
        }),
    )

    def description_preview(self, obj):
        if len(obj.description) > 50:
            return f"{obj.description[:50]}..."
        return obj.description
    description_preview.short_description = "Description"

    def modules_count(self, obj):
        if obj.modules:
            count = len(obj.modules)
            total_duration = sum(module.get('duration', 0) for module in obj.modules)
            return f"{count} modules ({total_duration} days)"
        return "0 modules"
    modules_count.short_description = "Modules"

    def process_count(self, obj):
        if obj.process:
            count = len(obj.process)
            return f"{count} step{'s' if count != 1 else ''}"
        return "0 steps"
    process_count.short_description = "Process Steps"


@admin.register(Testomonial, site=admin_site)
class TestomonialAdmin(admin.ModelAdmin):
    list_display = ("name", "uni", "location", "rating_display", "message_preview", "image_preview")
    search_fields = ("name", "uni", "location", "message")
    list_filter = ("rating", "uni", "location")
    ordering = ("-rating", "name")

    fieldsets = (
        ("Student Information", {
            "fields": ("name", "img", "uni", "location")
        }),
        ("Testimonial", {
            "fields": ("rating", "message")
        }),
    )

    def rating_display(self, obj):
        rating_map = {"one": 1, "two": 2, "three": 3, "four": 4, "five": 5}
        stars = "⭐" * rating_map.get(obj.rating, 0)
        return f"{stars} ({obj.rating})"
    rating_display.short_description = "Rating"

    def message_preview(self, obj):
        if len(obj.message) > 60:
            return f"{obj.message[:60]}..."
        return obj.message
    message_preview.short_description = "Message"

    def image_preview(self, obj):
        if obj.img:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 50%;" />',
                obj.img.url
            )
        return "No Image"
    image_preview.short_description = "Photo"


@admin.register(SendUsMessage, site=admin_site)
class SendUsMessageAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "email",
        "contact_number",
        "service",
        "message",
        "created_at",
    )
    search_fields = ("full_name", "email", "contact_number")
    list_filter = ("service", "created_at")
    readonly_fields = ("created_at",)


@admin.register(ConsultancyForm, site=admin_site)
class ConsultancyFormAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "email",
        "contact_number",
        "education",
        "country",
        "test",
    )
    search_fields = ("full_name", "email", "contact_number", "education")
    list_filter = ("education", "country", "test")



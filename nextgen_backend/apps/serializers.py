from rest_framework import serializers
from .models import (
    Organization,
    Position,
    Team,
    About,
    AboutVideo,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    SendUsMessage,
    ConsultancyForm,
    Hero,
    Experience,
)


class OrganizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "address",
            "logo",
            "contact_number",
            "email",
            "pan_no",
            "reg_no",
            "description",
        ]


class PositionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Position
        fields = ["id", "name"]


class TeamSerializer(serializers.ModelSerializer):
    position = PositionSerializer(read_only=True)
    position_id = serializers.PrimaryKeyRelatedField(
        queryset=Position.objects.all(), source="position", write_only=True
    )

    class Meta:
        model = Team
        fields = [
            "id",
            "first_name",
            "middle_name",
            "last_name",
            "image",
            "position",
            "position_id",
            "experience",
            "uploaded_at",
        ]


class AboutVideoSerializer(serializers.ModelSerializer):
    class Meta:
        model = AboutVideo
        fields = ["id", "about", "video", "order", "uploaded_at"]


class AboutSerializer(serializers.ModelSerializer):
    videos = AboutVideoSerializer(many=True, read_only=True)

    class Meta:
        model = About
        fields = ["id", "story", "mission", "vision", "image", "videos", "uploaded_at"]


class PopupSerializer(serializers.ModelSerializer):
    class Meta:
        model = Popup
        fields = ["id", "image", "video", "uploaded_at"]


class UniversitiesSerializer(serializers.ModelSerializer):
    class Meta:
        model = Universities
        fields = [
            "id",
            "name",
            "image",
            "ranking",
            "about",
            "program_offered",
            "admission_required",
            "cost",
            "scholarship",
        ]


class DestinationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Destination
        fields = [
            "id",
            "name",
            "image",
            "programs",
            "uni",
            "flag",
            "description",
            "why_study",
            "top_universities",
            "popular_courses",
            "cost_of_study",
        ]


class ServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = [
            "id",
            "name",
            "svg",
            "description",
            "why_to_choose",
            "offer",
            "process",
        ]


class TestPreparationSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestPreparation
        fields = [
            "id",
            "name",
            "description",
            "about",
            "modules",
            "process",
            "features",
        ]


class TestomonialSerializer(serializers.ModelSerializer):
    class Meta:
        model = Testomonial
        fields = ["id", "name", "img", "uni", "location", "rating", "message"]


class SendUsMessageSerializer(serializers.ModelSerializer):
    service_name = serializers.CharField(source="service.name", read_only=True)

    class Meta:
        model = SendUsMessage
        fields = [
            "id",
            "full_name",
            "email",
            "contact_number",
            "service",
            "service_name",
            "message",
            "created_at",
        ]
        read_only_fields = ["created_at"]


class ConsultancyFormSerializer(serializers.ModelSerializer):
    country_name = serializers.CharField(source="country.name", read_only=True)
    test_name = serializers.CharField(source="test.name", read_only=True)

    class Meta:
        model = ConsultancyForm
        fields = [
            "id",
            "full_name",
            "contact_number",
            "email",
            "education",
            "country",
            "country_name",
            "test",
            "test_name",
            "message",
        ]


class HeroSerializer(serializers.ModelSerializer):
    class Meta:
        model = Hero
        fields = ["id", "title", "description", "image", "video", "uploaded_at"]


class ExperienceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Experience
        fields = ["id", "title", "about", "experience", "image", "uploaded_at"]

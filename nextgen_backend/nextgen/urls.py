"""
URL configuration for nextgen project.
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.views.static import serve
from apps.admin import admin_site



# Define specific URL patterns BEFORE the admin catch-all
urlpatterns = [
    path('api/', include('apps.urls')),
    path('ckeditor5/', include('django_ckeditor_5.urls')),
    re_path(r"^media/(?P<path>.*)$", serve, {"document_root": settings.MEDIA_ROOT}),
    re_path(r"^static/(?P<path>.*)$", serve, {"document_root": settings.STATIC_ROOT}),
]

# Custom Admin at root - MUST be last because it catches everything else
urlpatterns += [
    path('', admin_site.urls),
]
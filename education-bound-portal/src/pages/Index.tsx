import Navigation from '@/components/Navigation';
import HeroSection from '@/components/HeroSection';
import AboutSection from '@/components/AboutSection';
import DestinationsSection from '@/components/DestinationsSection';
import ServicesSection from '@/components/ServicesSection';
import TestimonialsSection from '@/components/TestimonialsSection';
import RegistrationForm from '@/components/RegistrationForm';
import ContactSection from '@/components/ContactSection';

import { useState, useEffect } from 'react';
import { getOrganizations } from '@/components/APis/ApiService';
import type { Organization } from '@/types/api';

const getcompanyInfo = async () => {
  try {
    const data = await getOrganizations();
    return data;
  } catch (error) {
    console.error('Error fetching company info:', error);
    return null;
  }
}

const Index = () => {
  const [companyInfo, setCompanyInfo] = useState<Organization | null>(null);

  useEffect(() => {
    const fetchCompanyInfo = async () => {
      const data = await getcompanyInfo();
      if (data && Array.isArray(data) && data.length > 0) {
        setCompanyInfo(data[0]);
      }
    };
    fetchCompanyInfo();
  }, []);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <Navigation />
      <HeroSection />
      <AboutSection />
      <DestinationsSection />
      <ServicesSection />
      <TestimonialsSection />
      <RegistrationForm />
      <ContactSection />
      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-8 border-t border-gray-800 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div className='flex flex-col items-center md:items-start'>
              <img 
                src={companyInfo?.logo } 
                alt={companyInfo?.name } 
                className="h-12 w-auto rounded-full mb-4"
              />
              <p className="text-gray-400 text-sm">
                {companyInfo?.description || "Your trusted partner for international education and study abroad guidance."}
              </p>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="/about" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="/destinations" className="hover:text-white transition-colors">Destinations</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Study Abroad Counseling</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Visa Processing</a></li>
                <li><a href="#" className="hover:text-white transition-colors">SOP Guidance</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Language Test Prep</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Contact Info</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li>{companyInfo?.contact_number || "N/A"}</li>
                <li>{companyInfo?.email || "<EMAIL>"}</li>
                <li>{companyInfo?.address || "123 Education Street, City Center"}</li>
                <li>PAN: {companyInfo?.pan_no || "N/A"}</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 dark:border-gray-700 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2024 {companyInfo?.name || "NextGen Hub Pvt Ltd"}. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;

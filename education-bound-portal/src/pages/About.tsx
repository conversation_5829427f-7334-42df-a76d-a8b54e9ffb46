import { useEffect, useState } from 'react';
import Navigation from '@/components/Navigation';
import { getReq } from '@/components/APis/ApiService';
import TestimonialsSection from '@/components/TestimonialsSection';

interface Position {
  id: number;
  name: string;
}

interface TeamMember {
  id: number;
  first_name: string;
  middle_name?: string;
  last_name: string;
  image: string;
  position: Position;
  experience: string;
  uploaded_at: string;
}

interface AboutVideo {
  id: number;
  about: number;
  video: string | null;
  order: number;
  uploaded_at: string;
}

interface AboutData {
  id: number;
  story: string;
  mission: string[] | null;
  vision: string[] | null;
  image: string | null;
  videos: AboutVideo[];
  uploaded_at: string;
}

const About = () => {
  const [about, setAbout] = useState<AboutData | null>(null);
  const [team, setTeam] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [teamLoading, setTeamLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [teamError, setTeamError] = useState<string | null>(null);
  const [currentVideoIndex, setCurrentVideoIndex] = useState<number>(0);
  const [showVideo, setShowVideo] = useState<boolean>(false);

  useEffect(() => {
   
    setLoading(true);
    
    // Fetch about data
    getReq('about/')
      .then((data) => {
     
        if (Array.isArray(data) && data.length > 0) {
          setAbout(data[0]);
        } else if (data === null) {
          console.error('About.tsx: API call returned null (likely an error in getReq)');
          setError('Failed to load data. API returned null.');
        } else {
          console.warn('About.tsx: No data found or data is not an array:', data);
          setError('No about information found.');
          setAbout(null);
        }
      })
      .catch((err) => {
        console.error('About.tsx: Error fetching data:', err);
        setError('Failed to load about information.');
      })
      .finally(() => {
        setLoading(false);
      
      });

    // Fetch team data
    setTeamLoading(true);
    getReq('team/')
      .then((data) => {
       
        if (Array.isArray(data)) {
          setTeam(data);
        } else if (data === null) {
          console.error('About.tsx: Team API call returned null');
          setTeamError('Failed to load team data. API returned null.');
        } else {
          console.warn('About.tsx: Team data is not an array:', data);
          setTeamError('No team information found.');
          setTeam([]);
        }
      })
      .catch((err) => {
        console.error('About.tsx: Error fetching team data:', err);
        setTeamError('Failed to load team information.');
      })
      .finally(() => {
        setTeamLoading(false);
       
      });
  }, []);

  // Video slideshow effect
  useEffect(() => {
    if (!about?.videos || about.videos.length === 0) return;

    // Start with image for 3 seconds, then switch to videos
    const initialTimer = setTimeout(() => {
      setShowVideo(true);
    }, 3000);

    return () => clearTimeout(initialTimer);
  }, [about]);

  useEffect(() => {
    if (!about?.videos || about.videos.length <= 1 || !showVideo) return;

    const interval = setInterval(() => {
      setCurrentVideoIndex((prevIndex) =>
        (prevIndex + 1) % about.videos.length
      );
    }, 8000); // Switch video every 8 seconds

    return () => clearInterval(interval);
  }, [about?.videos, showVideo]);

  const getFullName = (member: TeamMember) => {
    const parts = [member.first_name, member.middle_name, member.last_name].filter(Boolean);
    return parts.join(' ');
  };

  const getTeamImageUrl = (imageUrl: string) => {
    // If the image URL is already a full URL, use it as is
    // If it's a relative path, it should work with your domain
    return imageUrl || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face";
  };

  const getVideoUrl = (videoUrl: string) => {
    // If the video URL is already a full URL, use it as is
    // If it's a relative path, it should work with your domain
    return videoUrl;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 text-center">
          <p className="text-xl text-gray-600 dark:text-gray-300">Loading about information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 text-center">
          <p className="text-xl text-red-600 dark:text-red-400">Error: {error}</p>
        </div>
      </div>
    );
  }

  if (!about) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 text-center">
          <p className="text-xl text-gray-600 dark:text-gray-300">No about information available.</p>
        </div>
      </div>
    );
  }

  const storyImageUrl = about.image 
    ? about.image 
    : "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=600&h=400&fit=crop";
  const storyImageAlt = about.image ? "Our Story - NextGen Hub" : "Students studying - Placeholder";

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <Navigation />
      
      <div className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
               About US
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Your trusted partner in achieving international education dreams
              </p>
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Our Story</h2>
                <div className="text-gray-600 dark:text-gray-300 mb-4" dangerouslySetInnerHTML={{ __html: about.story }} />
              </div>
              <div className="relative">
                {/* Image/Video Container with smooth transitions */}
                <div className="relative rounded-lg shadow-lg overflow-hidden bg-gray-200 dark:bg-gray-700">
                  {/* About Image */}
                  <div
                    className={`transition-opacity duration-1000 ${showVideo ? 'opacity-0 absolute inset-0' : 'opacity-100'}`}
                  >
                    <img
                      src={storyImageUrl}
                      alt={storyImageAlt}
                      className="w-full h-auto object-cover"
                    />
                  </div>

                  {/* Video Slideshow */}
                  {about.videos && about.videos.length > 0 && (
                    <div
                      className={`transition-opacity duration-1000 ${showVideo ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}
                    >
                      {about.videos
                        .sort((a, b) => a.order - b.order)
                        .map((video, index) => (
                          <div
                            key={video.id}
                            className={`transition-opacity duration-1000 ${
                              index === currentVideoIndex ? 'opacity-100' : 'opacity-0 absolute inset-0'
                            }`}
                          >
                            {video.video && (
                              <video
                                autoPlay
                                muted
                                loop
                                className="w-full h-auto object-cover"
                                style={{ aspectRatio: 'auto' }}
                              >
                                <source src={getVideoUrl(video.video)} type="video/mp4" />
                                Your browser does not support the video tag.
                              </video>
                            )}
                          </div>
                        ))}
                    </div>
                  )}

                  {/* Video indicators */}
                  {about.videos && about.videos.length > 1 && showVideo && (
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                      {about.videos.map((_, index) => (
                        <div
                          key={index}
                          className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                            index === currentVideoIndex
                              ? 'bg-white'
                              : 'bg-white/50'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>



        {/* Mission & Vision */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 gap-12">
              <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Our Mission</h3>
                {about.mission && about.mission.length > 0 ? (
                  <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                    {about.mission.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-primary mr-2 mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-600 dark:text-gray-300">No mission statement available.</p>
                )}
              </div>
              <div className="bg-white dark:bg-gray-900 p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Our Vision</h3>
                {about.vision && about.vision.length > 0 ? (
                  <ul className="text-gray-600 dark:text-gray-300 space-y-2">
                    {about.vision.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-primary mr-2 mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-600 dark:text-gray-300">No vision statement available.</p>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Meet Our Team
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Experienced professionals dedicated to your success
              </p>
            </div>
            
            {teamLoading ? (
              <div className="text-center">
                <p className="text-lg text-gray-600 dark:text-gray-300">Loading team information...</p>
              </div>
            ) : teamError ? (
              <div className="text-center">
                <p className="text-lg text-red-600 dark:text-red-400">Error loading team: {teamError}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Showing placeholder team members
                </p>
                {/* Fallback to placeholder team */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
                  {[
                    { name: "Dr. Sarah Johnson", role: "Chief Education Counselor", experience: "15+ years" },
                    { name: "Michael Chen", role: "Visa Processing Expert", experience: "12+ years" },
                    { name: "Priya Sharma", role: "Test Preparation Specialist", experience: "10+ years" }
                  ].map((member, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                      <div className="text-center">
                        <div className="w-32 h-32 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <span className="text-2xl font-bold text-primary">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">{member.name}</h4>
                        <p className="text-primary font-medium mb-2">{member.role}</p>
                        <p className="text-gray-600 dark:text-gray-400">{member.experience}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : team.length === 0 ? (
              <div className="text-center">
                <p className="text-lg text-gray-600 dark:text-gray-300">No team members found.</p>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {team.map((member) => (
                  <div key={member.id} className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <div className="text-center">
                      <div className="relative mb-6">
                        <img 
                          src={getTeamImageUrl(member.image)}
                          alt={getFullName(member)}
                          className="w-32 h-32 rounded-full mx-auto object-cover border-4 border-primary/20 shadow-lg"
                          loading="lazy"
                        />
                        <div className="absolute -bottom-2 -right-2 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                          ✓
                        </div>
                      </div>
                      <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        {getFullName(member)}
                      </h4>
                      <p className="text-primary font-medium mb-2">
                        {member.position.name}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {member.experience} experience
                      </p>
                      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      </div>
      {/* Testimonial section  */}
      <TestimonialsSection />

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-8 border-t border-gray-800 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p>&copy; 2024 NextGen Hub Pvt Ltd. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default About;
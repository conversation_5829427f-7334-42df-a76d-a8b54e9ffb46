import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, MapPin, GraduationCap, DollarSign, Star, Users, BookOpen, Award, Globe } from 'lucide-react';
import Navigation from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { MediaImage } from '@/components/ui/media-image';
import { getUniversity } from '@/components/APis/ApiService';
import type { Universities, University } from '@/types/api';

// Helper function to transform backend data to frontend expectations
const transformUniversityData = (backendData: Universities): University => {
  return {
    ...backendData,
    description: backendData.about || '',
    location: 'Location not specified', // Default value since backend doesn't provide this
    type: 'University', // Default value since backend doesn't provide this
    programs_offered: backendData.program_offered || [],
    entry_requirements: backendData.admission_required || [],
    tuition_fees: backendData.cost || [],
    scholarships: backendData.scholarship || [],
  };
};


const UniversityDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [university, setUniversity] = useState<University | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUniversity = async () => {
      if (!id) {
        setError('University ID not provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getUniversity(parseInt(id));
        if (response) {
          // Transform the backend data to match frontend expectations
          const transformedData = transformUniversityData(response);
          setUniversity(transformedData);
        } else {
          setError('University not found');
        }
      } catch (error) {
        console.error('Error fetching university:', error);
        setError('Failed to load university details');
      } finally {
        setLoading(false);
      }
    };

    fetchUniversity();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <Navigation />
        <div className="pt-20 flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 dark:border-blue-800 mx-auto mb-6"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            </div>
            <p className="text-gray-600 dark:text-gray-300 text-lg">Loading university details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !university) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <Navigation />
        <div className="pt-20 flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <div className="text-8xl mb-8">🏛️</div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              {error || 'University Not Found'}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 text-lg mb-8">
              The university you're looking for doesn't exist or couldn't be loaded.
            </p>
            <Link
              to="/universities"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Universities
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-12">
        <div className="container mx-auto px-4">
          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="mb-6"
          >
            <Link
              to="/universities"
              className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Universities
            </Link>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex items-center gap-6 mb-8">
                <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-2xl">
                  <GraduationCap className="w-12 h-12 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-2">
                    {university.name}
                  </h1>
                  {university.location && (
                    <div className="flex items-center text-gray-600 dark:text-gray-300">
                      <MapPin className="w-5 h-5 mr-2" />
                      <span className="text-lg">{university.location}</span>
                    </div>
                  )}
                </div>
              </div>

              <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8">
                {university.description}
              </p>

              <div className="flex flex-wrap gap-4">
                <div className="flex items-center bg-white dark:bg-gray-800 rounded-lg px-4 py-2 shadow-sm">
                  <Award className="w-5 h-5 text-blue-600 mr-2" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Ranking: #{university.ranking}
                  </span>
                </div>
                {university.type && (
                  <div className="flex items-center bg-white dark:bg-gray-800 rounded-lg px-4 py-2 shadow-sm">
                    <Globe className="w-5 h-5 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {university.type}
                    </span>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Image */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="relative"
            >
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <MediaImage
                  src={university.image}
                  alt={university.name}
                  className="w-full h-96 object-cover"
                  fallbackSrc="https://images.unsplash.com/photo-1562774053-701939374585?w=1200&h=800&fit=crop"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-300">World Ranking</p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">#{university.ranking}</p>
                      </div>
                      <Award className="w-8 h-8 text-yellow-500" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="pb-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">

            {/* Programs Offered */}
            {university.programs_offered && university.programs_offered.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg"
              >
                <div className="flex items-center mb-6">
                  <div className="bg-green-100 dark:bg-green-900 p-3 rounded-lg mr-4">
                    <BookOpen className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Programs Offered
                  </h2>
                </div>
                <div className="space-y-3">
                  {university.programs_offered.map((program, index) => (
                    <div key={index} className="flex items-start">
                      <div className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-600 dark:text-gray-300">{program}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Entry Requirements */}
            {university.entry_requirements && university.entry_requirements.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg"
              >
                <div className="flex items-center mb-6">
                  <div className="bg-purple-100 dark:bg-purple-900 p-3 rounded-lg mr-4">
                    <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Entry Requirements
                  </h2>
                </div>
                <div className="space-y-3">
                  {university.entry_requirements.map((requirement, index) => (
                    <div key={index} className="flex items-start">
                      <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-600 dark:text-gray-300">{requirement}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </div>

          {/* Tuition Fees */}
          {university.tuition_fees && university.tuition_fees.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="mt-12 bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg"
            >
              <div className="flex items-center mb-8">
                <div className="bg-orange-100 dark:bg-orange-900 p-3 rounded-lg mr-4">
                  <DollarSign className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Tuition Fees
                </h2>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {university.tuition_fees.map((fee, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Tuition Fee</span>
                        <span className="text-lg font-bold text-gray-900 dark:text-white">{fee.tuition_fee}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Living Expense</span>
                        <span className="text-lg font-bold text-gray-900 dark:text-white">{fee.living_expense}</span>
                      </div>
                      {fee.cost && (
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Additional Cost</span>
                          <span className="text-lg font-bold text-gray-900 dark:text-white">{fee.cost}</span>
                        </div>
                      )}
                      {fee.expense && (
                        <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-orange-600 dark:text-orange-400">Total Expense</span>
                            <span className="text-xl font-bold text-orange-600 dark:text-orange-400">{fee.expense}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Scholarships */}
          {university.scholarships && university.scholarships.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-12 bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg"
            >
              <div className="flex items-center mb-8">
                <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-lg mr-4">
                  <Award className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Scholarships Available
                </h2>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                {university.scholarships.map((scholarship, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                    <div className="flex items-start">
                      <Star className="w-5 h-5 text-yellow-600 mt-1 mr-3 flex-shrink-0" />
                      <span className="text-gray-600 dark:text-gray-300">{scholarship}</span>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Apply to {university.name}?
            </h2>
            <p className="text-blue-100 text-lg mb-8">
              Get expert guidance on your application process and increase your chances of admission.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-blue-600 hover:bg-gray-100">
                Start Application
                <GraduationCap className="w-4 h-4 ml-2" />
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                Download Brochure
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default UniversityDetail;
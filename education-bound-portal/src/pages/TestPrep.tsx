import { useState, useEffect } from 'react';
import Navigation from '@/components/Navigation';
import { getReq } from '@/components/APis/ApiService';

interface TestPrepOption {
  id: string;
  name: string;
  description: string;
  duration?: string;
}

const TestPrep = () => {
  const [testPrepOptions, setTestPrepOptions] = useState<TestPrepOption[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestPrepOptions = async () => {
      try {
        setLoading(true);
        const data = await getReq('test-preparations/');
        
        if (data && Array.isArray(data)) {
          // Transform the API data to match the frontend structure
          const transformedData: TestPrepOption[] = data.map((item: any) => ({
            id: item.id.toString(),
            name: item.name,
            description: item.description,
            duration: '8-12 weeks' // Default duration since it's not in API response
          }));
          
          setTestPrepOptions(transformedData);
        }
      } catch (error) {
        console.error('Error fetching test prep options:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestPrepOptions();
  }, []);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <Navigation />
      
      <div className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                Test Preparation
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Achieve your target scores with our expert-led preparation courses
              </p>
            </div>
          </div>
        </section>

        {/* Test Prep Options */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            {loading ? (
              <div className="flex justify-center items-center min-h-[200px]">
                <div className="text-lg text-gray-600 dark:text-gray-300">Loading test preparations...</div>
              </div>
            ) : (
              <div className="grid md:grid-cols-2 gap-8">
                {testPrepOptions.map((test) => (
                  <div 
                    key={test.id}
                    className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300"
                  >
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">{test.name} Preparation</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">{test.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Duration: {test.duration}</span>
                      <a 
                        href={`/test-prep/${test.id}`}
                        className="inline-flex items-center text-primary hover:text-primary/80 font-medium"
                      >
                        Learn More
                        <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                        </svg>
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-8 border-t border-gray-800 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p>&copy; 2024 NextGen Hub Pvt Ltd. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TestPrep;
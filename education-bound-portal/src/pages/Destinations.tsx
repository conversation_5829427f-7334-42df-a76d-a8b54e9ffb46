import { useState, useEffect } from 'react';
import Navigation from '@/components/Navigation';
import { Link } from 'react-router-dom';
import { getReq } from '@/components/APis/ApiService';

interface Destination {
  id: number;
  name: string;
  image: string;
  programs: string;
  uni: string;
  description: string;
}

const Destinations = () => {
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        const response = await getReq('destinations/');
        if (response) {
          setDestinations(response);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Helper function to create slug from country name
  const createSlug = (name: string) => {
    return name.toLowerCase().replace(/\s+/g, '-');
  };

  // Helper function to get brief description from HTML
  // Helper function to get brief description from HTML
const getBriefDescription = (htmlDescription: string) => {
  // Remove HTML tags and decode common HTML entities
  let plainText = htmlDescription.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();

  // Remove "Overview" if it appears at the beginning
  if (plainText.toLowerCase().startsWith('overview')) {
    plainText = plainText.substring(8).trim(); // removes "overview" (8 chars)
    // Optional: also remove any punctuation like ':' or '-' after "Overview"
    plainText = plainText.replace(/^[:\-–—\s]+/, '');
  }

  const sentences = plainText.split(/[.!?]+/);
  const meaningfulSentence = sentences.find(s => s.trim().length > 20);

  return meaningfulSentence ? meaningfulSentence.trim() + '.' : plainText.substring(0, 100) + '...';
};

  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
        <Navigation />
        <div className="pt-20 flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">Loading destinations...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 transition-colors">
      <Navigation />
      
      <div className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                Study Destinations
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Explore top study destinations around the world
              </p>
            </div>
          </div>
        </section>

        {/* Destinations Grid */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {destinations.map((destination) => (
                <div 
                  key={destination.id}
                  className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-lg dark:hover:shadow-gray-900/20 transition-all duration-300"
                >
                  <img 
                    src={destination.image}
                    alt={destination.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{destination.name}</h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {getBriefDescription(destination.description)}
                    </p>
                    <div className="space-y-2 mb-6">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500 dark:text-gray-400">Universities:</span>
                        <span className="text-gray-700 dark:text-gray-300">{destination.uni}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500 dark:text-gray-400">Programs:</span>
                        <span className="text-gray-700 dark:text-gray-300">{destination.programs}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <Link 
                        to={`/destinations/${destination.id}`}
                        className="inline-flex items-center justify-center w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                      >
                        Learn More
                        <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                        </svg>
                      </Link>
                      
                      <a 
                        href="/contact"
                        className="inline-flex items-center justify-center w-full text-primary hover:text-primary/80 font-medium py-2 px-4 border border-primary hover:border-primary/80 rounded-lg transition-colors"
                      >
                        Explore Opportunities
                        <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"/>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 dark:bg-gray-950 text-white py-8 border-t border-gray-800 dark:border-gray-700">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p>&copy; 2024 NextGen Hub Pvt Ltd. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Destinations;
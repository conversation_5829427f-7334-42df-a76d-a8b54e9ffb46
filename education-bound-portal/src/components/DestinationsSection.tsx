import { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { ComposableMap, Geographies, Geography, Marker } from 'react-simple-maps';
import { getDestinations } from './APis/ApiService';
import type { Destination } from '@/types/api';

// World map topology URL
const geoUrl = "https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json";

// Country coordinates for markers and connections
const countryCoordinates = {
  'Nepal': [85.3240, 27.7172],
  'UK': [-0.1276, 51.5074],
  'USA': [-87.0369, 37.9072],
  'Canada': [-93.6972, 55.4215],
  'Australia': [149.1300, -35.2809],
  'Germany': [13.4050, 52.5200],
  'France': [2.3522, 48.8566],
  'Japan': [139.6917, 35.6895],
  'South Korea': [126.9780, 37.5665],
  'New Zealand': [174.7762, -41.2865],
  'Switzerland': [7.4474, 46.9470],
  'Netherlands': [4.9041, 52.3676],
  'Sweden': [18.0686, 59.3293],
  'Norway': [10.7522, 59.9139],
  'Denmark': [12.5683, 55.6761],
  'Finland': [24.9384, 60.1699],
  'Ireland': [-6.2603, 53.3498],
  'Belgium': [4.3517, 50.8503],
  'Austria': [16.3738, 48.2082],
  'Italy': [12.4964, 41.9028],
  'Spain': [-3.7038, 40.4168],
  'Portugal': [-9.1393, 38.7223],
  'Czech Republic': [14.4378, 50.0755],
  'Poland': [21.0122, 52.2297],
  'Hungary': [19.0402, 47.4979],
  'Slovenia': [14.5058, 46.0569],
  'Slovakia': [17.1077, 48.1486],
  'Croatia': [15.9819, 45.8150],
  'Estonia': [24.7536, 59.4370],
  'Latvia': [24.1052, 56.9496],
  'Lithuania': [25.2797, 54.6872],
};

// Remove local interface as we're using the one from types/api

const DestinationSection = () => {
  const navigate = useNavigate();
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loading, setLoading] = useState(true);
  const [hoveredCountry, setHoveredCountry] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        const response = await getDestinations();
        setDestinations(response);
      } catch (error) {
        console.error('Error fetching destinations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [hoverTimeout]);

  const handleMarkerEnter = useCallback((countryName: string) => {
    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }
    setHoveredCountry(countryName);
  }, [hoverTimeout]);

  const handleMarkerLeave = useCallback(() => {
    // Add a small delay before clearing hover to prevent flickering
    const timeout = setTimeout(() => {
      setHoveredCountry(null);
    }, 100);
    setHoverTimeout(timeout);
  }, []);

  const handleMarkerClick = useCallback((destination: Destination) => {
    navigate(`/destinations/${destination.id}`);
  }, [navigate]);





  // Memoize destination markers with stable handlers
  const destinationMarkers = useMemo(() => {
    return destinations.map((destination) => {
      const coords = countryCoordinates[destination.name];
      if (!coords) return null;

      const isHovered = hoveredCountry === destination.name;

      // Create stable handlers for this specific destination
      const onEnter = () => handleMarkerEnter(destination.name);
      const onLeave = () => handleMarkerLeave();
      const onClick = () => handleMarkerClick(destination);

      return (
        <Marker key={destination.id} coordinates={coords}>
          {/* Animated outer ring - only for destination countries */}
          <circle
            r={isHovered ? 16 : 12}
            fill="none"
            stroke="#dc2626"
            strokeWidth={isHovered ? 3 : 2}
            opacity={isHovered ? 0.8 : 0.5}
            className={isHovered ? "animate-pulse" : "animate-ping"}
          />
          {/* Secondary ring for enhanced effect */}
          {isHovered && (
            <circle
              r={20}
              fill="none"
              stroke="#dc2626"
              strokeWidth={1}
              opacity={0.3}
              className="animate-ping"
            />
          )}
          {/* Main marker dot */}
          <circle
            r={isHovered ? 8 : 6}
            fill="#dc2626"
            stroke="#ffffff"
            strokeWidth={2}
            className="cursor-pointer transition-all duration-200 drop-shadow-lg hover:fill-red-700"
            onMouseEnter={onEnter}
            onMouseLeave={onLeave}
            onClick={onClick}
            style={{
              filter: isHovered ? 'drop-shadow(0 0 8px rgba(220, 38, 38, 0.8))' : 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))'
            }}
          />
          {/* Country label with enhanced styling */}
          <text
            textAnchor="middle"
            y={-25}
            className={`text-xs lg:text-sm font-semibold pointer-events-none transition-all duration-200 ${
              isHovered
                ? 'fill-red-600 dark:fill-red-400 text-shadow'
                : 'fill-gray-800 dark:fill-gray-100'
            }`}
            style={{
              filter: isHovered ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5))' : 'drop-shadow(0 1px 1px rgba(0, 0, 0, 0.3))'
            }}
          >
            {destination.flag || '🏫'} {destination.name}
          </text>
        </Marker>
      );
    });
  }, [destinations, hoveredCountry, handleMarkerEnter, handleMarkerLeave]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
        <div className="pt-20 flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">Loading destinations...</p>
          </div>
        </div>
      </div>
    );
  }

  const nepalCoords = countryCoordinates['Nepal'];

  return (
    <section className="py-8 sm:py-12 lg:py-20 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <div className="container mx-auto px-4 lg:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 dark:text-gray-100 mb-4">
            Popular Study <span className='text-blue-600 dark:text-blue-400'>Destinations</span>
          </h2>
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Explore world-class education opportunities across our partner countries
          </p>
        </div>

        {/* Clean Map Container - No background container */}
        <div className="relative w-full mb-12">
          <div className="w-full" style={{ aspectRatio: '2/1', minHeight: '400px' }}>
            <ComposableMap
              projection="geoEqualEarth"
              projectionConfig={{
                scale: 160,
                center: [20, 0]
              }}
              width={1000}
              height={500}
              style={{ 
                width: "100%", 
                height: "100%"
              }}
            >
              <Geographies geography={geoUrl}>
                {({ geographies }) =>
                  geographies.map((geo) => {
                    const countryName = geo.properties.NAME;
                    const isDestination = destinations.some(d => d.name === countryName);
                    const isNepal = countryName === 'Nepal';

                    return (
                      <Geography
                        key={geo.rsmKey}
                        geography={geo}
                        fill={
                          isNepal
                            ? "#1e3a8a" // Darker blue for Nepal (origin country)
                            : isDestination
                              ? "#dc2626" // Red for destination countries
                              : "#a7c3e8" // Light blue for other countries (like reference)
                        }
                        stroke="#ffffff"
                        strokeWidth={0.5}
                        className="transition-all duration-300"
                        style={{
                          default: { 
                            outline: "none",
                            fill: isNepal 
                              ? "#1e3a8a" 
                              : isDestination 
                                ? "#dc2626" 
                                : "#a7c3e8"
                          },
                          hover: {
                            fill: isNepal 
                              ? "#1e40af" 
                              : isDestination 
                                ? "#b91c1c" 
                                : "#93c5fd",
                            outline: "none"
                          },
                          pressed: { outline: "none" }
                        }}
                      />
                    );
                  })
                }
              </Geographies>



              {/* Nepal marker - origin point */}
              {nepalCoords && (
                <Marker coordinates={nepalCoords}>
                  {/* Outer ring for Nepal */}
                  <circle
                    r={15}
                    fill="none"
                    stroke="#1e3a8a"
                    strokeWidth={3}
                    opacity={0.6}
                    className="animate-pulse"
                  />
                  {/* Inner ring */}
                  <circle
                    r={10}
                    fill="#1e3a8a"
                    stroke="#ffffff"
                    strokeWidth={3}
                    className="drop-shadow-lg"
                    style={{
                      filter: 'drop-shadow(0 0 10px rgba(30, 58, 138, 0.8))'
                    }}
                  />
                  {/* Center dot */}
                  <circle
                    r={4}
                    fill="#ffffff"
                    className="drop-shadow-sm"
                  />
                  {/* Nepal label */}
                  <text
                    textAnchor="middle"
                    y={-30}
                    className="text-sm lg:text-base font-bold fill-blue-800 dark:fill-blue-300 pointer-events-none"
                    style={{
                      filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5))'
                    }}
                  >
                    🇳🇵 Nepal (Origin)
                  </text>
                </Marker>
              )}

              {/* Destination markers with blinking animation */}
              {destinationMarkers}
            </ComposableMap>
          </div>
        </div>

        {/* Enhanced Stats section */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 lg:gap-8">
          <div className="text-center bg-white dark:bg-gray-800 p-6 lg:p-8 rounded-xl lg:rounded-2xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:scale-105">
            <div className="text-4xl lg:text-5xl font-bold text-blue-600 dark:text-blue-400 mb-3">
              {destinations.length}+
            </div>
            <div className="text-gray-600 dark:text-gray-300 font-medium text-lg">Study Destinations</div>
          </div>
          <div className="text-center bg-white dark:bg-gray-800 p-6 lg:p-8 rounded-xl lg:rounded-2xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:scale-105">
            <div className="text-4xl lg:text-5xl font-bold text-purple-600 dark:text-purple-400 mb-3">
              {destinations.reduce((total, dest) => {
                const programs = dest.programs.match(/\d+/);
                return total + (programs ? parseInt(programs[0]) : 0);
              }, 0)}+
            </div>
            <div className="text-gray-600 dark:text-gray-300 font-medium text-lg">Available Programs</div>
          </div>
          <div className="text-center bg-white dark:bg-gray-800 p-6 lg:p-8 rounded-xl lg:rounded-2xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:scale-105">
            <div className="text-4xl lg:text-5xl font-bold text-green-600 dark:text-green-400 mb-3">
              100%
            </div>
            <div className="text-gray-600 dark:text-gray-300 font-medium text-lg">Success Rate</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DestinationSection;
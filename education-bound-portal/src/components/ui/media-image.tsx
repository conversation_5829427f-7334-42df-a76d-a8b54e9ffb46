import React, { useState } from 'react';
import { getMediaUrl } from '@/components/APis/ApiService';

interface MediaImageProps {
  src: string | null;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  onLoad?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
}

/**
 * MediaImage component that handles media URLs from the Django backend
 * with proper fallback handling and loading states
 */
export const MediaImage: React.FC<MediaImageProps> = ({
  src,
  alt,
  className = '',
  fallbackSrc = 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=500&h=400&fit=crop',
  onError,
  onLoad,
  ...props
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setHasError(true);
    setIsLoading(false);
    if (onError) {
      onError(e);
    } else {
      // Set fallback image
      (e.target as HTMLImageElement).src = fallbackSrc;
    }
  };

  const handleLoad = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setIsLoading(false);
    if (onLoad) {
      onLoad(e);
    }
  };

  const imageSrc = getMediaUrl(src) || fallbackSrc;

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
    </div>
  );
};

interface MediaVideoProps {
  src: string | null;
  className?: string;
  fallbackSrc?: string;
  poster?: string | null;
  controls?: boolean;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  onError?: (e: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
  onLoad?: (e: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
}

/**
 * MediaVideo component that handles video URLs from the Django backend
 * with proper fallback handling
 */
export const MediaVideo: React.FC<MediaVideoProps> = ({
  src,
  className = '',
  fallbackSrc,
  poster,
  controls = true,
  autoPlay = false,
  muted = false,
  loop = false,
  onError,
  onLoad,
  ...props
}) => {
  const [hasError, setHasError] = useState(false);

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    setHasError(true);
    if (onError) {
      onError(e);
    } else if (fallbackSrc) {
      (e.target as HTMLVideoElement).src = fallbackSrc;
    }
  };

  const handleLoad = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    if (onLoad) {
      onLoad(e);
    }
  };

  const videoSrc = getMediaUrl(src) || fallbackSrc;
  const posterSrc = getMediaUrl(poster);

  if (!videoSrc) {
    return null;
  }

  return (
    <video
      src={videoSrc}
      poster={posterSrc || undefined}
      className={className}
      controls={controls}
      autoPlay={autoPlay}
      muted={muted}
      loop={loop}
      onError={handleError}
      onLoadedData={handleLoad}
      {...props}
    >
      Your browser does not support the video tag.
    </video>
  );
};

export default MediaImage;

import { useState, useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Play, X, ArrowRight } from 'lucide-react';
import { getReq } from './APis/ApiService';

interface HomepagePopupData {
  id: number;
  image: string;
  video: string;
}

// Interface that matches your actual API response (array of popup items)
interface ApiPopupResponse {
  id: number;
  image: string;
  video: string;
  uploaded_at: string;
}

const getHomepagePopup = async (): Promise<HomepagePopupData | null> => {
  try {
    const data = await getReq('popups/');
    console.log('Raw API Response:', data); // Debug log
    
    // Check if data is null (error case from getReq)
    if (!data) {
      console.error('getReq returned null');
      return null;
    }
    
    // Check if data is an array and has at least one item
    if (!Array.isArray(data) || data.length === 0) {
      console.error('API response is not an array or is empty:', data);
      return null;
    }
    
    // Get the first item from the array
    const popupItem = data[0];
    
    // Check if the first item has required properties
    if (!popupItem.id || !popupItem.image || !popupItem.video) {
      console.error('API response item missing required fields:', popupItem);
      return null;
    }
    
    // Transform the API response to match your component's expected structure
    const transformedData = {
      id: popupItem.id,
      image: popupItem.image,
      video: popupItem.video
    };
    
    console.log('Transformed popup data:', transformedData); // Debug log
    return transformedData;
  } catch (error) {
    console.error('Error in getHomepagePopup:', error);
    return null;
  }
};

const HomepagePopup = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [popupData, setPopupData] = useState<HomepagePopupData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch popup data
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getHomepagePopup();
        console.log('Processed popup data:', data); // Debug log
        setPopupData(data);
        
        // Show popup after 2 seconds if data is available
        if (data) {
          setTimeout(() => {
            setIsOpen(true);
          }, 2000);
        }
      } catch (error) {
        console.error('Failed to fetch popup data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []); // Empty dependency array - only run once on mount

  const handleVideoPlay = () => {
    setShowVideo(true);
  };

  const handleClose = () => {
    setIsOpen(false);
    setShowVideo(false);
  };

  // Don't render if loading or no data
  if (loading) {
    console.log('Component is loading...'); // Debug log
    return null;
  }
  
  if (!popupData) {
    console.log('No popup data available'); // Debug log
    return null;
  }
  
  console.log('Rendering popup with data:', popupData); // Debug log

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-4xl w-full mx-4 p-0 overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-2xl">
        {/* Close button */}
        {/* <button
          onClick={handleClose}
          className="absolute top-4 right-4 z-50 p-2 rounded-full bg-black/20 hover:bg-black/40 text-white transition-all duration-200 hover:scale-110"
          aria-label="Close popup"
        >
          <X size={20} />
        </button> */}

        <div className="relative">
          {!showVideo ? (
            // Hero Image with Call to Action
            <div className="relative h-[500px] bg-gradient-to-br from-primary/20 to-accent/20">
              <img
                src={popupData.image}
                alt="Students studying abroad"
                className="w-full h-full object-cover"
                onLoad={() => console.log('Image loaded successfully:', popupData.image)}
                onError={(e) => {
                  console.error('Image failed to load:', popupData.image);
                  console.error('Image error event:', e);
                  // Optionally set a fallback image
                  // e.currentTarget.src = '/fallback-image.jpg';
                }}
              />
              <div className="absolute inset-0 bg-black/40" />
              
              {/* Content overlay */}
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white px-8">
                <h2 className="text-3xl md:text-5xl font-bold text-center mb-4">
                  Start Your Dream Journey
                </h2>
                <p className="text-lg md:text-xl text-center mb-8 max-w-2xl">
                  Discover how thousands of students achieved their study abroad dreams with NextGen Hub
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 items-center">
                  <Button
                    onClick={handleVideoPlay}
                    className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg flex items-center gap-2 transform hover:scale-105 transition-all duration-200"
                  >
                    <Play size={20} />
                    Watch Success Stories
                  </Button>
                  
                  <Button
                    onClick={() => {
                      window.location.href = '/contact';
                      handleClose();
                    }}
                    variant="outline"
                    className="border-white text-blue-800 hover:bg-white hover:text-primary px-8 py-3 text-lg flex items-center gap-2 transform hover:scale-105 transition-all duration-200"
                  >
                    Book Free Consultation
                    <ArrowRight size={20} />
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // Video Player
            <div className="relative h-[500px] bg-black">
              <video
                controls
                autoPlay
                className="w-full h-full object-cover"
                poster={popupData.image}
                onLoadStart={() => console.log('Video started loading:', popupData.video)}
                onCanPlay={() => console.log('Video can play:', popupData.video)}
                onError={(e) => {
                  console.error('Video failed to load:', popupData.video);
                  console.error('Video error event:', e);
                }}
              >
                <source
                  src={popupData.video}
                  type="video/mp4"
                />
                Your browser does not support the video tag.
              </video>
              
              {/* Back to image button */}
              <button
                onClick={() => setShowVideo(false)}
                className="absolute top-4 left-4 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-all duration-200"
                aria-label="Back to image"
              >
                <ArrowRight size={20} className="rotate-180" />
              </button>
            </div>
          )}

          {/* Bottom CTA section */}
          <div className="p-6 bg-white dark:bg-gray-900 border-t">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Ready to Start Your Journey?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Join 500+ successful students who achieved their study abroad dreams
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={() => {
                    window.location.href = '/contact';
                    handleClose();
                  }}
                  className="bg-primary hover:bg-primary/90"
                >
                  Get Free Consultation
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    window.location.href = '/about';
                    handleClose();
                  }}
                >
                  Learn More About Us
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default HomepagePopup;
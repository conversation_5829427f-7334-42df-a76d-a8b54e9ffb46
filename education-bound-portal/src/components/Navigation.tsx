import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { MessageCircle, ChevronDown, ChevronRight, X, Menu, Globe, GraduationCap } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { getReq } from '@/components/APis/ApiService';
import logo from '../images/icon.png'

interface CompanyData {
  id: number;
  name: string;
  logo: string;
  contact_number: string;
  email: string;
  pan_no: string;
  reg_no: string;
  address: string;
  description: string;
}

interface Destination {
  id: number;
  name: string;
  image: string;
  programs: string;
  uni: string;
  description: string;
  flag?: string;
}

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState({
    services: false,
    testPrep: false,
    destinations: false
  });

  // Company data state
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [companyLoading, setCompanyLoading] = useState(true);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [destinationsLoading, setDestinationsLoading] = useState(true);
  const [destinationsError, setDestinationsError] = useState(null);

  // Dynamic services state
  const [services, setServices] = useState([]);
  const [servicesLoading, setServicesLoading] = useState(true);
  const [servicesError, setServicesError] = useState(null);
  const [loading, setLoading] = useState(true);

  // Dynamic test preparation state
  const [testPrep, setTestPrep] = useState([]);
  const [testPrepLoading, setTestPrepLoading] = useState(true);
  const [testPrepError, setTestPrepError] = useState(null);

  // Static navigation items (removed destinations from here)
  const staticNavItems = [
    { to: "/", label: "Home" },
    { to: "/about", label: "About" },
    { to: "/universities", label: "Universities" },
    { to: "/contact", label: "Contact", emoji: "📞" }
  ];

  // Dynamic dropdown configurations
  const dropdownConfigs = [
    {
      key: 'services',
      label: 'Services',
      icon: GraduationCap,
      items: services,
      loading: servicesLoading,
      error: servicesError,
      loadingText: 'Loading services...',
      emptyText: 'No services found.',
      basePath: '/services'
    },
    {
      key: 'testPrep',
      label: 'Test Prep',
      items: testPrep,
      loading: testPrepLoading,
      error: testPrepError,
      loadingText: 'Loading courses...',
      emptyText: 'No test prep courses found.',
      basePath: '/test-prep'
    }
  ];

  // Data fetching functions
  const fetchData = async (endpoint, setter, setLoading, setError) => {
    setLoading(true);
    try {
      const data = await getReq(endpoint);
      setter(Array.isArray(data) ? data : []);
    } catch (err) {
      setError(`Failed to load ${endpoint}`);
      setter([]);
    } finally {
      setLoading(false);
    }
  };

  // Fixed company data fetching function
  const fetchCompanyData = async () => {
    setCompanyLoading(true);
    try {
      const data = await getReq('organizations/');
      if (Array.isArray(data) && data.length > 0) {
        setCompanyData(data[0]);
      } else {
        console.warn('No company data found');
        setCompanyData(null);
      }
    } catch (error) {
      console.error('Error fetching company data:', error);
      setCompanyData(null);
    } finally {
      setCompanyLoading(false);
    }
  };

  // Fetch destinations
  useEffect(() => {
    const fetchDestinations = async () => {
      setDestinationsLoading(true);
      try {
        const response = await getReq('destinations/');
        if (response) {
          setDestinations(response as Destination[]);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
        setDestinationsError('Failed to load destinations');
        setDestinations([]);
      } finally {
        setDestinationsLoading(false);
        setLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Fetch company data on mount
  useEffect(() => {
    fetchCompanyData();
  }, []);

  // Fetch services
  useEffect(() => {
    fetchData('services/', setServices, setServicesLoading, setServicesError);
  }, []);

  // Fetch test preparation courses
  useEffect(() => {
    fetchData('test-preparations/', setTestPrep, setTestPrepLoading, setTestPrepError);
  }, []);

  // Fixed WhatsApp click handler with dynamic number
  const handleWhatsAppClick = () => {
    // Use company phone number if available, otherwise fallback
    const phoneNumber = companyData?.contact_number || '9779876543210';

    // Clean the phone number (remove spaces, dashes, etc.)
    const cleanedNumber = phoneNumber.replace(/\D/g, '');

    // Ensure it starts with country code
    const formattedNumber = cleanedNumber.startsWith('977') ? cleanedNumber : `977${cleanedNumber}`;

    const message = encodeURIComponent('Hi, I would like to know more about study abroad opportunities.');
    const whatsappUrl = `https://wa.me/${formattedNumber}?text=${message}`;

    window.open(whatsappUrl, '_blank');
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
    setExpandedMenus({ services: false, testPrep: false, destinations: false });
  };

  const toggleSubmenu = (menuKey) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  // Render static navigation link
  const renderNavLink = (item, additionalClasses = "", onClick = null) => {
    const { to, label, icon: Icon, emoji } = item;
    return (
      <Link
        key={to}
        to={to}
        onClick={onClick}
        className={`text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-semibold hover:scale-105 px-4 py-2.5 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 relative group flex items-center ${additionalClasses}`}
      >
        {Icon && <Icon className="mr-2 h-4 w-4" />}
        {emoji && <span className="mr-2">{emoji}</span>}
        {label}
        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
      </Link>
    );
  };

  // Render destinations dropdown (special large layout)
  const renderDestinationsDropdown = () => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-semibold hover:scale-105 px-4 py-2.5 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900 group relative data-[state=open]:text-primary data-[state=open]:bg-gray-50 dark:data-[state=open]:bg-gray-800/50">
          <Globe className="mr-2 h-4 w-4" />
          Destinations
          <ChevronDown className="ml-1 h-4 w-4 transition-transform duration-300 group-data-[state=open]:rotate-180" />
          <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 shadow-2xl rounded-xl min-w-[600px] max-w-[800px] animate-in slide-in-from-top-2 duration-300 z-50 p-6">
          {destinationsLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">Loading destinations...</div>
            </div>
          )}
          {destinationsError && (
            <div className="flex items-center justify-center py-8">
              <div className="text-red-500">{destinationsError}</div>
            </div>
          )}
          {!destinationsLoading && !destinationsError && destinations.length === 0 && (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">No destinations found.</div>
            </div>
          )}
          {!destinationsLoading && !destinationsError && destinations.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {destinations.map((destination) => (
                <DropdownMenuItem key={destination.id} className="p-0 focus:bg-transparent hover:bg-transparent">
                  <Link
                    to={`/destinations/${destination.id}`}
                    className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50/80 dark:hover:bg-gray-700/50 transition-all duration-300 group w-full text-center min-h-[100px] justify-center focus:outline-none focus:ring-2 focus:ring-primary/50 focus:bg-gray-50/80 dark:focus:bg-gray-700/50"
                  >
                    <div className="flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                      {destination.flag ? (
                        <span className="text-3xl">{destination.flag}</span>
                      ) : (
                        <div className="w-12 h-8 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                          <Globe className="w-4 h-4 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary dark:group-hover:text-primary transition-colors duration-300 leading-tight">
                      {destination.name}
                    </span>
                  </Link>
                </DropdownMenuItem>
              ))}
            </div>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render dropdown menu (for services and test prep)
  const renderDropdown = (config) => {
    const { key, label, icon: Icon, items, loading, error, loadingText, emptyText, basePath } = config;

    return (
      <DropdownMenu key={key}>
        <DropdownMenuTrigger className="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-semibold hover:scale-105 px-4 py-2.5 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900 group relative data-[state=open]:text-primary data-[state=open]:bg-gray-50 dark:data-[state=open]:bg-gray-800/50">
          {Icon && <Icon className="mr-2 h-4 w-4" />}
          {label}
          <ChevronDown className="ml-1 h-4 w-4 transition-transform duration-300 group-data-[state=open]:rotate-180" />
          <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 shadow-2xl rounded-xl min-w-[280px] animate-in slide-in-from-top-2 duration-300 z-50 p-2">
          <div className="grid gap-1">
            {loading && (
              <div className="px-4 py-2 text-gray-400 text-sm">{loadingText}</div>
            )}
            {error && (
              <div className="px-4 py-2 text-red-500 text-sm">{error}</div>
            )}
            {!loading && !error && items.length === 0 && (
              <div className="px-4 py-2 text-gray-400 text-sm">{emptyText}</div>
            )}
            {items.map((item) => (
              <DropdownMenuItem key={item.id || item.name} className="hover:bg-gray-50/80 dark:hover:bg-gray-700/50 transition-all duration-200 focus:bg-gray-50/80 dark:focus:bg-gray-700/50 rounded-lg p-0">
                <Link
                  to={`${basePath}/${item.id}`}
                  className="w-full flex items-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary py-3 px-4 rounded-lg transition-all duration-200 group"
                >
                  <span className="font-medium">{item.name}</span>
                </Link>
              </DropdownMenuItem>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render mobile navigation link
  const renderMobileNavLink = (item) => {
    const { to, label, icon: Icon, emoji } = item;
    return (
      <Link
        key={to}
        to={to}
        onClick={closeMenu}
        className="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-gray-50/80 dark:hover:bg-gray-800/50 transition-all duration-300 font-semibold rounded-xl text-base focus:outline-none focus:ring-2 focus:ring-primary/50 focus:bg-gray-50/80 dark:focus:bg-gray-800/50 group"
      >
        {Icon && <Icon className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200" />}
        {emoji && <span className="mr-3 text-lg group-hover:scale-110 transition-transform duration-200">{emoji}</span>}
        {label}
      </Link>
    );
  };
  
  // Render mobile destinations section
  const renderMobileDestinationsSection = () => {
    return (
      <div className="space-y-1">
        <button
          onClick={() => toggleSubmenu('destinations')}
          className="w-full flex items-center justify-between px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-gray-50/80 dark:hover:bg-gray-800/50 transition-all duration-300 font-semibold rounded-xl text-base focus:outline-none focus:ring-2 focus:ring-primary/50 focus:bg-gray-50/80 dark:focus:bg-gray-800/50 group"
          aria-expanded={expandedMenus.destinations}
          aria-controls="destinations-submenu"
        >
          <div className="flex items-center">
            <Globe className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
            <span>Destinations</span>
          </div>
          <ChevronRight
            className={`h-5 w-5 transition-transform duration-300 ${expandedMenus.destinations ? 'rotate-90' : ''
              }`}
          />
        </button>
        <div
          id="destinations-submenu"
          className={`transition-all duration-500 ease-in-out ${expandedMenus.destinations
              ? 'max-h-96 opacity-100 visible'
              : 'max-h-0 opacity-0 invisible overflow-hidden'
            }`}
        >
          <div className="ml-4 space-y-1 border-l-2 border-primary/30 pl-4 py-2">
            {destinationsLoading && (
              <div className="px-4 py-2 text-gray-400 text-sm">Loading destinations...</div>
            )}
            {destinationsError && (
              <div className="px-4 py-2 text-red-500 text-sm">{destinationsError}</div>
            )}
            {!destinationsLoading && !destinationsError && destinations.length === 0 && (
              <div className="px-4 py-2 text-gray-400 text-sm">No destinations found.</div>
            )}
            {destinations.map((destination) => (
              <Link
                key={destination.id}
                to={`/destinations/${destination.id}`}
                onClick={closeMenu}
                className="flex items-center px-4 py-2.5 text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary hover:bg-gray-50/80 dark:hover:bg-gray-800/50 transition-all duration-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:bg-gray-50/80 dark:focus:bg-gray-800/50 group font-medium"
              >
                {destination.flag && (
                  <img
                    src={destination.flag}
                    alt={`${destination.name} flag`}
                    className="w-6 h-4 object-cover rounded mr-3 shadow-sm"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                )}
                {destination.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Render mobile collapsible section
  const renderMobileCollapsibleSection = (config) => {
    const { key, label, icon: Icon, items, loading, error, loadingText, emptyText, basePath } = config;

    return (
      <div key={key} className="space-y-1">
        <button
          onClick={() => toggleSubmenu(key)}
          className="w-full flex items-center justify-between px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-gray-50/80 dark:hover:bg-gray-800/50 transition-all duration-300 font-semibold rounded-xl text-base focus:outline-none focus:ring-2 focus:ring-primary/50 focus:bg-gray-50/80 dark:focus:bg-gray-800/50 group"
          aria-expanded={expandedMenus[key]}
          aria-controls={`${key}-submenu`}
        >
          <div className="flex items-center">
            {Icon && <Icon className="mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200" />}
            <span>{label}</span>
          </div>
          <ChevronRight
            className={`h-5 w-5 transition-transform duration-300 ${expandedMenus[key] ? 'rotate-90' : ''
              }`}
          />
        </button>
        <div
          id={`${key}-submenu`}
          className={`transition-all duration-500 ease-in-out ${expandedMenus[key]
              ? 'max-h-96 opacity-100 visible'
              : 'max-h-0 opacity-0 invisible overflow-hidden'
            }`}
        >
          <div className="ml-4 space-y-1 border-l-2 border-primary/30 pl-4 py-2">
            {loading && (
              <div className="px-4 py-2 text-gray-400 text-sm">{loadingText}</div>
            )}
            {error && (
              <div className="px-4 py-2 text-red-500 text-sm">{error}</div>
            )}
            {!loading && !error && items.length === 0 && (
              <div className="px-4 py-2 text-gray-400 text-sm">{emptyText}</div>
            )}
            {items.map((item) => (
              <Link
                key={item.id || item.name}
                to={`${basePath}/${item.id}`}
                onClick={closeMenu}
                className="flex items-center px-4 py-2.5 text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary hover:bg-gray-50/80 dark:hover:bg-gray-800/50 transition-all duration-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:bg-gray-50/80 dark:focus:bg-gray-800/50 group font-medium"
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <nav className="fixed top-0 left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg z-50 transition-all duration-300 border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo - Left side with proper error handling */}
            <div className="flex-shrink-0">
              {companyLoading ? (
                <div className="h-10 w-10 sm:h-12 sm:w-12 lg:h-16 lg:w-16 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              ) : companyData?.logo ? (
                <img
                  src={logo}
                  alt={companyData.name || "Company Logo"}
                  className="h-10 w-auto rounded-full sm:h-12 lg:h-16 cursor-pointer transition-all duration-300 hover:scale-105 object-contain drop-shadow-sm"
                  onClick={() => window.location.href = '/'}
                  onError={(e) => {
                    console.error('Logo failed to load:', companyData.logo);
                    // Hide the image if it fails to load
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              ) : (
                <div
                  className="h-10 w-10 sm:h-12 sm:w-12 lg:h-16 lg:w-16 bg-primary text-white rounded-full flex items-center justify-center cursor-pointer transition-all duration-300 hover:scale-105 font-bold text-lg"
                  onClick={() => window.location.href = '/'}
                  title={companyData?.name || "NextGen Hub"}
                >
                  {(companyData?.name || 'NH').charAt(0).toUpperCase()}
                </div>
              )}
            </div>

            {/* Desktop Menu - Right side */}
            <div className="hidden xl:flex items-center space-x-1 2xl:space-x-2">
              {/* Static navigation items */}
              {staticNavItems.slice(0, 2).map(item => renderNavLink(item))}

              {/* Destinations dropdown */}
              {renderDestinationsDropdown()}

              {/* Dynamic dropdown menus */}
              {dropdownConfigs.map(config => renderDropdown(config))}

              {/* Remaining static navigation items */}
              {staticNavItems.slice(2).map(item => renderNavLink(item))}

              <div className="ml-4 flex items-center space-x-3">
                <ThemeToggle />
                <Button
                  onClick={() => window.location.href = '/contact'}
                  className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 px-6 py-2.5 font-semibold focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900 rounded-lg relative overflow-hidden group"
                >
                  <span className="relative z-10 flex items-center">
                    Book Consultation
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>
              </div>
            </div>

            {/* Mobile Menu Controls */}
            <div className="flex items-center space-x-3 xl:hidden">
              <ThemeToggle />

              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="inline-flex items-center justify-center p-2.5 rounded-lg text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transform hover:scale-105"
                aria-expanded={isMenuOpen}
                aria-label="Toggle navigation menu"
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6 transition-transform duration-300 rotate-90" />
                ) : (
                  <Menu className="h-6 w-6 transition-transform duration-300" />
                )}
              </button>
            </div>
          </div>
          {/* Enhanced Mobile Menu */}
          <div className={`xl:hidden transition-all duration-500 ease-in-out ${isMenuOpen
              ? 'max-h-screen opacity-100 visible'
              : 'max-h-0 opacity-0 invisible overflow-hidden'
            }`}>
            <div className="px-2 pt-4 pb-6 space-y-2 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200/50 dark:border-gray-700/50 rounded-b-xl">

              {/* Static mobile navigation items */}
              {staticNavItems.slice(0, 2).map(item => renderMobileNavLink(item))}

              {/* Mobile destinations section */}
              {renderMobileDestinationsSection()}

              {/* Dynamic collapsible sections */}
              {dropdownConfigs.map(config => renderMobileCollapsibleSection(config))}

              {/* Remaining static mobile navigation items */}
              {staticNavItems.slice(2).map(item => renderMobileNavLink(item))}

              <div className="pt-4 pb-2">
                <Button
                  onClick={() => {
                    window.location.href = '/contact';
                    closeMenu();
                  }}
                  className="w-full bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-300 py-3.5 font-semibold text-base focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900 rounded-xl relative overflow-hidden group"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Book Consultation
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>
      {/* Enhanced WhatsApp Floating Button with dynamic number */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={handleWhatsAppClick}
          className="bg-gradient-to-br from-green-400 via-green-500 to-green-600 hover:from-green-500 hover:via-green-600 hover:to-green-700 text-white p-4 rounded-full shadow-2xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-2 focus:outline-none focus:ring-4 focus:ring-green-300/50 dark:focus:ring-green-600/50 group active:scale-95 animate-pulse hover:animate-none"
          aria-label="Contact us on WhatsApp"
          title={`Chat with us on WhatsApp${companyData?.contact_number ? ` (${companyData.contact_number})` : ''}`}
        >
          <MessageCircle
            size={28}
            className="transition-all duration-300 group-hover:rotate-12 group-hover:scale-110 drop-shadow-sm"
          />
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping"></div>
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full"></div>
        </button>
      </div>

      {/* Enhanced Overlay for mobile menu */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 xl:hidden transition-all duration-300"
          onClick={closeMenu}
          aria-hidden="true"
        />
      )}
    </>
  );
};

export default Navigation;
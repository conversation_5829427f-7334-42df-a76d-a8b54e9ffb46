import React, { useState, useEffect } from 'react';
import { motion, Variants } from 'framer-motion';
import { getServices } from './APis/ApiService';
import type { Service } from '@/types/api';

const ServicesSection = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const response = await getServices();
        setServices(response);
        setError(null);
      } catch (err) {
        setError('Failed to load services');
        console.error('Error fetching services:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  const parseOffersToFeatures = (offerHtml: string | undefined): string[] => {
    if (!offerHtml) return [];

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = offerHtml;

    const features: string[] = [];
    const listItems = tempDiv.querySelectorAll('li');

    listItems.forEach(li => {
      const titleElement = li.querySelector('p:first-child');
      if (titleElement && titleElement.textContent?.trim()) {
        const title = titleElement.textContent.trim();
        if (title && title.length > 3 && !title.includes('&nbsp;')) {
          features.push(title);
        }
      }
    });

    return features.slice(0, 3);
  };

  const getDescriptionExcerpt = (description: string): string => {
    if (!description) return '';

    const sentences = description.split(/[.!?]+/);
    const firstSentence = sentences[0]?.trim();

    if (firstSentence && firstSentence.length > 20) {
      return firstSentence + '.';
    }

    return description.length > 120
      ? description.substring(0, 120).trim() + '...'
      : description;
  };

  const renderSVG = (svgString: string) => {
    if (!svgString) return null;

    let htmlSVG = svgString
      .replace(/className=/g, 'class=')
      .replace(/fillRule=/g, 'fill-rule=')
      .replace(/clipRule=/g, 'clip-rule=')
      .replace(/viewBox=/g, 'viewBox=')
      .replace(/strokeWidth=/g, 'stroke-width=')
      .replace(/strokeLinecap=/g, 'stroke-linecap=')
      .replace(/strokeLinejoin=/g, 'stroke-linejoin=');

    return <div dangerouslySetInnerHTML={{ __html: htmlSVG }} />;
  };
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.15
    }
  }
};

const cardVariants = {
  hidden: {
    opacity: 0,
    y: 50,
    scale: 0.9
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.42, 0, 0.58, 1] as [number, number, number, number]
    }
  }
};

const cardHoverVariants = {
  rest: {
    scale: 1,
    y: 0,
    boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
  },
  hover: {
    scale: 1.03,
    y: -12,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1] as [number, number, number, number]
    }
  }
};

const iconVariants = {
  rest: {
    scale: 1,
    rotate: 0
  },
  hover: {
    scale: 1.2,
    rotate: 10,
    transition: {
      duration: 0.3,
      ease: [0.17, 0.67, 0.83, 0.67] as [number, number, number, number]
    }
  }
};

const buttonVariants: Variants = {
  rest: {
    backgroundColor: '#f8fafc',
    color: '#334155'
  },
  hover: {
    backgroundColor: '#2563eb',
    color: '#ffffff',
    transition: {
      duration: 0.3,
      ease: "easeOut" // this is fine as a string
    }
  }
};  

  if (loading) {
    return (
      <section id="services" className="py-20 bg-white dark:bg-gray-900 transition-colors">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              className="rounded-full h-12 w-12 border-b-2 mx-auto"
              style={{ borderColor: '#2563eb' }}
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <p className="mt-4 text-gray-600 dark:text-gray-300">Loading services...</p>
          </motion.div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="services" className="py-20 bg-white dark:bg-gray-900 transition-colors">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p style={{ color: '#ef4444' }}>{error}</p>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section id="services" className="py-20 bg-gray-50 dark:bg-gray-900 transition-colors">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#2563eb' }}>
            Our <span style={{ color: '#ef4444' }}>Services</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Comprehensive support for your entire study abroad journey
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {services.map((service) => {
            const features = parseOffersToFeatures(service.offer);
            const shortDescription = getDescriptionExcerpt(service.description);

            return (
              <motion.div
                key={service.id}
                className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 overflow-hidden"
                variants={cardVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div
                  variants={cardHoverVariants}
                  className="h-full"
                >
                  {/* Header Section */}
                  <div className="p-6 pb-4">
                    <div className="flex items-start justify-between mb-4">
                      <motion.div
                        className="w-14 h-14 rounded-xl flex items-center justify-center flex-shrink-0"
                        style={{ backgroundColor: '#2563eb20' }}
                        variants={iconVariants}
                      >
                        <div style={{ color: '#2563eb' }}>
                          {renderSVG(service.svg)}
                        </div>
                      </motion.div>
                    </div>

                    <h3 className="text-xl font-bold mb-3 leading-tight" style={{ color: '#2563eb' }}>
                      {service.name}
                    </h3>

                    <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                      {shortDescription}
                    </p>
                  </div>

                  {/* Features Section */}
                  {features.length > 0 && (
                    <div className="px-6 pb-4">
                      <div className="space-y-2">
                        {features.map((feature, idx) => (
                          <motion.div
                            key={idx}
                            className="flex items-start text-sm"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: idx * 0.1 }}
                          >
                            <div
                              className="w-1.5 h-1.5 rounded-full mt-2 mr-3 flex-shrink-0"
                              style={{ backgroundColor: '#2563eb' }}
                            ></div>
                            <span className="text-gray-700 dark:text-gray-300 leading-relaxed">
                              {feature}
                            </span>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Footer Section */}
                  <div className="px-6 pb-6 pt-2">
                    <div className="border-t border-gray-100 dark:border-gray-700 pt-4">
                      <motion.a
                        href={`/services/${service.id}`}
                        className="inline-flex items-center justify-center w-full px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-300"
                        variants={buttonVariants}
                        whileHover="hover"
                        initial="rest"
                        style={{
                          backgroundColor: '#f8fafc',
                          color: '#334155'
                        }}
                      >
                        Learn More & Get Started
                        <motion.svg
                          className="w-4 h-4 ml-2"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          whileHover={{ x: 4 }}
                          transition={{ duration: 0.2 }}
                        >
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </motion.svg>
                      </motion.a>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
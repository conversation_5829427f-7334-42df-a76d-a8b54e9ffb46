import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { getDestinations, getTestPreparations, submitConsultancyForm } from '@/components/APis/ApiService';
import type { Destination, TestPreparation, ConsultancyFormData } from '@/types/api';

interface FormData {
  full_name: string;
  contact_number: string;
  email: string;
  education: '12' | 'Diploma' | 'Bachelor' | 'Masters' | 'Phd' | '';
  country: number | '';
  test: number | '';
  message: string;
}

const RegistrationForm = () => {
  const [formData, setFormData] = useState<FormData>({
    full_name: '',
    contact_number: '',
    email: '',
    education: '',
    country: '',
    test: '',
    message: ''
  });

  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [tests, setTests] = useState<TestPreparation[]>([]);
  const [loading, setLoading] = useState(false);
  
  const { toast } = useToast();

  // Load destinations and tests on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [destinationsData, testsData] = await Promise.all([
          getDestinations(),
          getTestPreparations()
        ]);
        setDestinations(destinationsData);
        setTests(testsData);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load form data. Please refresh the page.",
          variant: "destructive"
        });
      }
    };

    loadData();
  }, [toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Validate required fields
    if (!formData.full_name || !formData.contact_number || !formData.email || 
        !formData.education || !formData.country || !formData.test) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      setLoading(false);
      return;
    }

    // Validate phone number (10 digits)
    if (!/^\d{10}$/.test(formData.contact_number)) {
      toast({
        title: "Invalid Phone Number",
        description: "Phone number must be exactly 10 digits.",
        variant: "destructive"
      });
      setLoading(false);
      return;
    }

    try {
      // Transform the data to match Django model expectations
      const consultancyData: ConsultancyFormData = {
        full_name: formData.full_name,
        contact_number: formData.contact_number,
        email: formData.email,
        education: formData.education as '12' | 'Diploma' | 'Bachelor' | 'Masters' | 'Phd',
        country: formData.country as number,
        test: formData.test as number,
        message: formData.message
      };

      const result = await submitConsultancyForm(consultancyData);

      if (result) {
        toast({
          title: "Registration Successful!",
          description: "Thank you for your interest. Our counselor will contact you within 24 hours.",
        });

        // Reset form
        setFormData({
          full_name: '',
          contact_number: '',
          email: '',
          education: '',
          country: '',
          test: '',
          message: ''
        });
      } else {
        throw new Error('Submission failed');
      }
    } catch (error) {
      toast({
        title: "Registration Failed",
        description: "There was an error submitting your registration. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'country' || name === 'test' ? (value === '' ? '' : parseInt(value)) : value
    });
  };

  return (
    <section id="registration" className="py-20 bg-white dark:bg-gray-900 transition-colors">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Start Your Journey Today
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Fill out this form and our expert counselors will reach out to you
          </p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-700 transition-colors">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Phone Number (10 digits) *
                </label>
                <input
                  type="tel"
                  name="contact_number"
                  value={formData.contact_number}
                  onChange={handleChange}
                  required
                  pattern="[0-9]{10}"
                  maxLength={10}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
                  placeholder="9876543210"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Current Education Level *
                </label>
                <select
                  name="education"
                  value={formData.education}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
                >
                  <option value="">Select your education level</option>
                  <option value="12">12 Grade</option>
                  <option value="Diploma">Diploma</option>
                  <option value="Bachelor">Bachelor</option>
                  <option value="Masters">Masters</option>
                  <option value="Phd">Phd</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Preferred Country *
                </label>
                <select
                  name="country"
                  value={formData.country}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
                >
                  <option value="">Select preferred country</option>
                  {destinations.map((destination) => (
                    <option key={destination.id} value={destination.id}>
                      {destination.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  English Test Taken *
                </label>
                <select
                  name="test"
                  value={formData.test}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
                >
                  <option value="">Select test taken</option>
                  {tests.map((test) => (
                    <option key={test.id} value={test.id}>
                      {test.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                How can we help you? *
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleChange}
                rows={4}
                required
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors resize-vertical"
                placeholder="Tell us about your study abroad goals, questions, or any specific requirements..."
              />
            </div>

            <div className="text-center">
              <Button 
                type="submit"
                size="lg"
                disabled={loading}
                className="bg-primary hover:bg-primary/90 text-white px-12 py-4 text-lg disabled:opacity-50"
              >
                {loading ? 'Submitting...' : 'Submit Registration'}
              </Button>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                Our counselor will contact you within 24 hours
              </p>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default RegistrationForm;
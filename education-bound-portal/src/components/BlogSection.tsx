
const BlogSection = () => {
  const blogPosts = [
    {
      title: "Complete Guide to Student Visa Process 2024",
      excerpt: "Everything you need to know about applying for student visas across different countries...",
      image: "https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?w=400&h=250&fit=crop",
      category: "Visa Guide",
      date: "Dec 15, 2024",
      readTime: "8 min read"
    },
    {
      title: "Australia vs Canada: Which is Better for International Students?",
      excerpt: "A comprehensive comparison of study opportunities, costs, and post-graduation prospects...",
      image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=400&h=250&fit=crop",
      category: "Country Comparison",
      date: "Dec 12, 2024",
      readTime: "6 min read"
    },
    {
      title: "How to Write a Winning Statement of Purpose",
      excerpt: "Expert tips and examples to craft a compelling SOP that gets you noticed by admissions committees...",
      image: "https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?w=400&h=250&fit=crop",
      category: "Application Tips",
      date: "Dec 10, 2024",
      readTime: "5 min read"
    },
    {
      title: "Top Scholarships for International Students in 2024",
      excerpt: "Discover merit-based and need-based scholarships that can fund your international education...",
      image: "https://images.unsplash.com/photo-1627556592933-b0c09593e26c?w=400&h=250&fit=crop",
      category: "Scholarships",
      date: "Dec 8, 2024",
      readTime: "7 min read"
    }
  ];

  return (
    <section id="blog" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Latest Insights & Guides
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Stay updated with the latest trends, tips, and insights in international education
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 mb-12">
          {blogPosts.map((post, index) => (
            <article 
              key={post.title}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group"
            >
              <div className="relative overflow-hidden">
                <img 
                  src={post.image}
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                    {post.category}
                  </span>
                </div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <time>{post.date}</time>
                  <span className="mx-2">•</span>
                  <span>{post.readTime}</span>
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                
                <button className="text-primary font-medium hover:text-primary/80 transition-colors">
                  Read More →
                </button>
              </div>
            </article>
          ))}
        </div>

        <div className="text-center">
          <button className="bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary/90 transition-colors">
            View All Articles
          </button>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;

import { toast } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import { MEDIA_URL } from "./api";
import type {
  Organization,
  Position,
  Team,
  About,
  Popup,
  Universities,
  Destination,
  Service,
  TestPreparation,
  Testimonial,
  SendUsMessage,
  ConsultancyForm,
  Hero,
  Experience,
  SendUsMessageForm,
  ConsultancyFormData,
  ApiResponse
} from '../../types/api';


// JSON POST request
export const postReq = async (url: string, data: unknown) => {
    // Assuming postReq might also need to use the proxy or a configurable base URL
    const fullUrl = `/api/${url}`;
    console.log(`ApiService: Making POST request to ${fullUrl}`);
    try {
        const response = await fetch(fullUrl, { // Use fullUrl for proxy
            method: 'POST',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });
        console.log(`ApiService: Response received from POST ${fullUrl}`, response);

        const responseData = await response.json();
        console.log(`ApiService: Parsed JSON data from POST ${fullUrl}`, responseData);

        if (response.ok && responseData.status === "200") { // Check response.ok as well
            toast.success(responseData.message);
            return { data: responseData, status: "200" };
        }

        const errorMsg = responseData.message || `HTTP error ${response.status}`;
        toast.error(errorMsg);
        return { status: String(response.status || "400"), message: errorMsg }; // Return more info
    } catch (error: any) {
        console.error(`ApiService: Catch block error for POST ${fullUrl}:`, error);
        toast.error(error.message || 'An error occurred during POST request');
        return { status: "400", message: error.message || 'An error occurred' };
    }
};

// GET request
export const getReq = async (url: string) => {
    const fullUrl = `/api/${url}`; // Use relative path for Vite proxy
    console.log(`ApiService: Making GET request to ${fullUrl}`);
    try {
        const response = await fetch(fullUrl);
        console.log(`ApiService: Response received from GET ${fullUrl}`, {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            url: response.url,
            headers: Object.fromEntries(response.headers.entries()),
        });

        if (!response.ok) {
            let errorText = response.statusText;
            try {
                errorText = await response.text();
            } catch (textError) {
                console.warn(`ApiService: Could not read error response body for ${fullUrl}`, textError);
            }
            console.error(`ApiService: HTTP error! Status: ${response.status}, Body: ${errorText}`);
            toast.error(`HTTP error ${response.status}: ${errorText || 'Failed to fetch'}`);
            return null; 
        }

        const data = await response.json();
        console.log(`ApiService: Parsed JSON data from GET ${fullUrl}`, data);
        return data;
    } catch (error: any) {
        console.error(`ApiService: Catch block error for GET ${fullUrl}:`, error);
        toast.error(error.message || 'An error occurred while fetching data.');
        return null;
    }
};

// Multipart/form-data POST request
export const postReqMultipart = async (url: string, formData: FormData) => {
    const fullUrl = `/api/${url}`; // Use relative path for Vite proxy
    console.log(`ApiService: Making Multipart POST request to ${fullUrl}`);
    try {
        const response = await fetch(fullUrl, {
            method: 'POST',
            body: formData
        });
        console.log(`ApiService: Response received from Multipart POST ${fullUrl}`, response);

        const responseData = await response.json();
        console.log(`ApiService: Parsed JSON data from Multipart POST ${fullUrl}`, responseData);

        if (response.ok && responseData.status === "200") { // Check response.ok
            toast.success(responseData.message);
            return { data: responseData, status: "200" };
        }
        const errorMsg = responseData.message || `HTTP error ${response.status}`;
        toast.error(errorMsg);
        return { status: String(response.status || "400"), message: errorMsg };

    } catch (error: any) {
        console.error(`ApiService: Catch block error for Multipart POST ${fullUrl}:`, error);
        toast.error(error.message || 'An error occurred during multipart POST request');
        return { status: "400", message: error.message || 'An error occurred' };
    }
};

// Helper function to get full media URL
export const getMediaUrl = (path: string | null): string | null => {
    if (!path) return null;
    if (path.startsWith('http')) return path;
    return `${MEDIA_URL}${path}`;
};

// ============================================================================
// SPECIFIC API METHODS FOR EACH ENDPOINT
// ============================================================================

// Organization API
export const getOrganizations = async (): Promise<Organization[]> => {
    const data = await getReq('organizations/');
    return data || [];
};

export const getOrganization = async (id: number): Promise<Organization | null> => {
    const data = await getReq(`organizations/${id}/`);
    return data;
};

// Position API
export const getPositions = async (): Promise<Position[]> => {
    const data = await getReq('positions/');
    return data || [];
};

// Team API
export const getTeamMembers = async (): Promise<Team[]> => {
    const data = await getReq('team/');
    return data || [];
};

export const getTeamMember = async (id: number): Promise<Team | null> => {
    const data = await getReq(`team/${id}/`);
    return data;
};

// About API
export const getAboutSections = async (): Promise<About[]> => {
    const data = await getReq('about/');
    return data || [];
};

export const getAboutSection = async (id: number): Promise<About | null> => {
    const data = await getReq(`about/${id}/`);
    return data;
};

// Popup API
export const getPopups = async (): Promise<Popup[]> => {
    const data = await getReq('popups/');
    return data || [];
};

// Universities API
export const getUniversities = async (): Promise<Universities[]> => {
    const data = await getReq('universities/');
    return data || [];
};

export const getUniversity = async (id: number): Promise<Universities | null> => {
    const data = await getReq(`universities/${id}/`);
    return data;
};

// Destinations API
export const getDestinations = async (): Promise<Destination[]> => {
    const data = await getReq('destinations/');
    return data || [];
};

export const getDestination = async (id: number): Promise<Destination | null> => {
    const data = await getReq(`destinations/${id}/`);
    return data;
};

// Services API
export const getServices = async (): Promise<Service[]> => {
    const data = await getReq('services/');
    return data || [];
};

export const getService = async (id: number): Promise<Service | null> => {
    const data = await getReq(`services/${id}/`);
    return data;
};

// Test Preparations API
export const getTestPreparations = async (): Promise<TestPreparation[]> => {
    const data = await getReq('test-preparations/');
    return data || [];
};

export const getTestPreparation = async (id: number): Promise<TestPreparation | null> => {
    const data = await getReq(`test-preparations/${id}/`);
    return data;
};

// Testimonials API
export const getTestimonials = async (): Promise<Testimonial[]> => {
    const data = await getReq('testimonials/');
    return data || [];
};

// Heroes API
export const getHeroes = async (): Promise<Hero[]> => {
    const data = await getReq('heroes/');
    return data || [];
};

export const getHero = async (id: number): Promise<Hero | null> => {
    const data = await getReq(`heroes/${id}/`);
    return data;
};

// Experiences API
export const getExperiences = async (): Promise<Experience[]> => {
    const data = await getReq('experiences/');
    return data || [];
};

export const getExperience = async (id: number): Promise<Experience | null> => {
    const data = await getReq(`experiences/${id}/`);
    return data;
};

// ============================================================================
// FORM SUBMISSION METHODS
// ============================================================================

// Send Us Message Form
export const submitSendUsMessage = async (formData: SendUsMessageForm): Promise<ApiResponse<SendUsMessage> | null> => {
    const response = await postReq('send-messages/', formData);
    if (response && response.status === "200") {
        return response as ApiResponse<SendUsMessage>;
    }
    return null;
};

// Consultancy Form
export const submitConsultancyForm = async (formData: ConsultancyFormData): Promise<ApiResponse<ConsultancyForm> | null> => {
    const response = await postReq('consultancy-forms/', formData);
    if (response && response.status === "200") {
        return response as ApiResponse<ConsultancyForm>;
    }
    return null;
};

// ============================================================================
// UTILITY METHODS
// ============================================================================

// Get rating as number for testimonials
export const getRatingNumber = (rating: string): number => {
    const ratingMap: { [key: string]: number } = {
        'one': 1,
        'two': 2,
        'three': 3,
        'four': 4,
        'five': 5
    };
    return ratingMap[rating] || 0;
};

// Format education level for display
export const formatEducationLevel = (education: string): string => {
    const educationMap: { [key: string]: string } = {
        '12': '12th Grade',
        'Diploma': 'Diploma',
        'Bachelor': 'Bachelor\'s Degree',
        'Masters': 'Master\'s Degree',
        'Phd': 'PhD'
    };
    return educationMap[education] || education;
};

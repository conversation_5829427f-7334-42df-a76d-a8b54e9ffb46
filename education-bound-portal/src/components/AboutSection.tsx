import React, { useState, useEffect } from 'react';
import { motion, easeOut } from 'framer-motion';
import { MediaImage } from '@/components/ui/media-image';
import { getExperiences, getMediaUrl } from './APis/ApiService';
import type { Experience } from '@/types/api';

const AboutSection = () => {
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchExperiences = async () => {
      try {
        const data = await getExperiences();
        setExperiences(data);
      } catch (error) {
        console.error('Failed to fetch experiences:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easeOut
      }
    }
  };

  const cardHoverVariants = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.05,
      y: -8,
      transition: {
        duration: 0.3,
        ease: easeOut
      }
    }
  };

  const iconVariants = {
    rest: { rotate: 0, scale: 1 },
    hover: {
      rotate: 360,
      scale: 1.1,
      transition: {
        duration: 0.6,
        ease: easeOut
      }
    }
  };

  return (
    <>
      <style>{`
        .experience-content p {
          margin-bottom: 1rem;
          line-height: 1.6;
        }
        
        .experience-content ul {
          margin-top: 1rem;
          list-style: none;
          padding-left: 0;
        }
        
        .experience-content li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 0.5rem;
          position: relative;
          padding-left: 1.5rem;
        }
        
        .experience-content li::before {
          content: "✓";
          color: #10b981;
          font-weight: bold;
          position: absolute;
          left: 0;
          top: 0.125rem;
          flex-shrink: 0;
        }
        
        .experience-content li p {
          margin-bottom: 0;
          margin-left: 0;
        }
        
        .experience-content span {
          /* Preserve any inline styles from CKEditor */
        }
      `}</style>
      <section id="about" className="py-20 bg-white dark:bg-gray-900 transition-colors">
        <div className="container mx-auto px-4">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#2563eb' }}>
              Why Choose <span style={{ color: '#ef4444' }}>Next Gen Hub ?</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              With years of experience and a proven track record, we're your trusted partner in making your study abroad dreams come true.
            </p>
          </motion.div>

          <motion.div 
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#2563eb20' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#2563eb' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Expert Guidance</h3>
              <p className="text-gray-600 dark:text-gray-300">Certified counselors with deep knowledge of international education systems</p>
            </motion.div>

            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#ef444420' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#ef4444' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Comprehensive Services</h3>
              <p className="text-gray-600 dark:text-gray-300">End-to-end support from university selection to visa approval</p>
            </motion.div>

            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#2563eb20' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#2563eb' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Global Network</h3>
              <p className="text-gray-600 dark:text-gray-300">Strong partnerships with universities worldwide for better opportunities</p>
            </motion.div>

            <motion.div 
              className="text-center group"
              variants={itemVariants}
              whileHover="hover"
              initial="rest"
            >
              <motion.div 
                className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-colors"
                style={{ backgroundColor: '#ef444420' }}
                variants={cardHoverVariants}
              >
                <motion.div
                  variants={iconVariants}
                  style={{ color: '#ef4444' }}
                >
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
                    <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
                  </svg>
                </motion.div>
              </motion.div>
              <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Success Rate</h3>
              <p className="text-gray-600 dark:text-gray-300">95% visa success rate with thousands of satisfied students</p>
            </motion.div>
          </motion.div>

          {/* Dynamic Experience Section */}
          {loading ? (
            <motion.div 
              className="mt-16 bg-gray-50 dark:bg-gray-800 rounded-lg p-8 transition-colors"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="animate-pulse">
                <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-4"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            </motion.div>
          ) : experiences.length > 0 ? (
            <>
              {experiences.map((experience, index) => (
                <motion.div 
                  key={experience.id} 
                  className={`${index === 0 ? 'mt-16' : 'mt-8'} bg-gray-50 dark:bg-gray-800 rounded-lg p-8 transition-colors`}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ 
                    duration: 0.6, 
                    delay: index * 0.2,
                    ease: "easeOut"
                  }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="grid md:grid-cols-2 gap-8 items-center">
                    <motion.div
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.2 + 0.3 }}
                    >
                      <h3 className="text-2xl font-bold mb-4" style={{ color: '#2563eb' }}>
                        {experience.title}
                      </h3>
                      <div 
                        className="experience-content text-gray-600 dark:text-gray-300"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    </motion.div>
                    <motion.div
                      initial={{ opacity: 0, x: 30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.2 + 0.4 }}
                      whileHover={{ scale: 1.05 }}
                    >
                      <MediaImage
                        src={experience.image}
                        alt={experience.title}
                        className="rounded-lg shadow-lg w-full h-64 object-cover"
                        fallbackSrc="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=500&h=400&fit=crop"
                      />
                    </motion.div>
                  </div>
                </motion.div>
              ))}
            </>
          ) : (
            <motion.div 
              className="mt-16 bg-gray-50 dark:bg-gray-800 rounded-lg p-8 transition-colors"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h3 className="text-2xl font-bold mb-4" style={{ color: '#2563eb' }}>Our Experience</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    With over a decade of experience in international education consultancy, NextGen Hub has helped thousands of students achieve their study abroad goals across 10+ countries.
                  </p>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-2" style={{ color: '#10b981' }} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                      Certified education consultants
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-2" style={{ color: '#10b981' }} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                      Partnerships with 200+ universities
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-2" style={{ color: '#10b981' }} fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                      </svg>
                      24/7 student support
                    </li>
                  </ul>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <img 
                    src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=500&h=400&fit=crop" 
                    alt="Team meeting" 
                    className="rounded-lg shadow-lg w-full"
                  />
                </motion.div>
              </div>
            </motion.div>
          )}
        </div>
      </section>
    </>
  );
};

export default AboutSection;
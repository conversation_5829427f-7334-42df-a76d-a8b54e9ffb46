// TypeScript interfaces for API responses based on Django backend models

export interface Organization {
  id: number;
  name: string;
  address: string;
  logo: string | null;
  contact_number: string;
  email: string;
  pan_no: string | null;
  reg_no: string | null;
  description: string | null;
}

export interface Position {
  id: number;
  name: string;
}

export interface Team {
  id: number;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  image: string | null;
  position: Position;
  position_id: number;
  experience: string;
  uploaded_at: string;
}

export interface AboutVideo {
  id: number;
  about: number;
  video: string | null;
  order: number;
  uploaded_at: string;
}

export interface About {
  id: number;
  story: string;
  mission: string[] | null;
  vision: string[] | null;
  image: string | null;
  videos: AboutVideo[];
  uploaded_at: string;
}

export interface Popup {
  id: number;
  image: string | null;
  video: string | null;
  uploaded_at: string;
}

export interface CostItem {
  tuition_fee: string;
  cost: string;
  living_expense: string;
  expense: string;
}

export interface Universities {
  id: number;
  name: string;
  image: string | null;
  ranking: string;
  about: string | null;
  program_offered: string[] | null;
  admission_required: string[] | null;
  cost: CostItem[] | null;
  scholarship: string[] | null;
}

// Create an alias for backward compatibility and to match component expectations
export interface University extends Universities {
  // Map backend fields to frontend expectations
  description?: string; // maps to 'about'
  location?: string; // not provided by backend, will be handled gracefully
  type?: string; // not provided by backend, will be handled gracefully
  programs_offered?: string[]; // maps to 'program_offered'
  entry_requirements?: string[]; // maps to 'admission_required'
  tuition_fees?: CostItem[]; // maps to 'cost'
  scholarships?: string[]; // maps to 'scholarship'
}

export interface DestinationCostItem extends CostItem {
  total_expense: string;
  total_cost: string;
}

export interface Destination {
  id: number;
  name: string;
  image: string | null;
  programs: string;
  uni: string;
  flag: string;
  description: string | null;
  why_study: string[] | null;
  top_universities: string[] | null;
  popular_courses: string[] | null;
  cost_of_study: DestinationCostItem[] | null;
}

export interface ServiceOffer {
  service: string;
  summary: string;
}

export interface ServiceProcess {
  process: string;
  summary: string;
}

export interface Service {
  id: number;
  name: string;
  svg: string | null;
  description: string;
  why_to_choose: string[] | null;
  offer: ServiceOffer[] | null;
  process: ServiceProcess[] | null;
}

export interface TestModule {
  name: string;
  summary: string;
  duration: number;
  section: number;
}

export interface TestFeature {
  name: string;
  summary: string;
}

export interface TestPreparation {
  id: number;
  name: string;
  description: string;
  about: string;
  modules: TestModule[] | null;
  process: TestFeature[] | null;
  features: TestFeature[] | null;
}

export interface Testimonial {
  id: number;
  name: string;
  img: string | null;
  uni: string;
  location: string;
  rating: 'one' | 'two' | 'three' | 'four' | 'five';
  message: string;
}

export interface SendUsMessage {
  id: number;
  full_name: string;
  email: string;
  contact_number: string;
  service: number;
  service_name: string;
  message: string;
  created_at: string;
}

export interface ConsultancyForm {
  id: number;
  full_name: string;
  contact_number: string;
  email: string;
  education: '12' | 'Diploma' | 'Bachelor' | 'Masters' | 'Phd';
  country: number;
  country_name: string;
  test: number;
  test_name: string;
  message: string;
}

export interface Hero {
  id: number;
  title: string;
  description: string;
  image: string | null;
  video: string | null;
  uploaded_at: string;
}

export interface Experience {
  id: number;
  title: string;
  about: string | null;
  experience: string[] | null;
  image: string | null;
  uploaded_at: string;
}

// Form data interfaces for POST requests
export interface SendUsMessageForm {
  full_name: string;
  email: string;
  contact_number: string;
  service: number;
  message: string;
}

export interface ConsultancyFormData {
  full_name: string;
  contact_number: string;
  email: string;
  education: '12' | 'Diploma' | 'Bachelor' | 'Masters' | 'Phd';
  country: number;
  test: number;
  message: string;
}

// API Response wrapper
export interface ApiResponse<T> {
  data: T;
  status: string;
  message?: string;
}

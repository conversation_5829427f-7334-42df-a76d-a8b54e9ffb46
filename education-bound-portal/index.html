<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Primary SEO Meta Tags -->
    <title>NextGen Hub - Best Study Abroad Consultants in Nepal | IELTS PTE Training | Visa Services Kohalpur</title>
    <meta name="description" content="NextGen Hub - Leading study abroad consultancy in Kohalpur, Banke, Nepal. Expert IELTS/PTE training, visa processing, university admissions for Australia, Canada, UK, US, Germany. 100% success rate in student visa applications." />
    <meta name="keywords" content="study abroad consultants Nepal, IELTS training Kohalpur, PTE coaching Nepal, student visa consultancy Banke, education consultants Nepal, overseas education Kohalpur, Australia student visa Nepal, Canada study visa consultants, UK university admission Nepal, US college admission consultancy, Germany education consultants, study abroad agency Nepal, international education consultants, visa processing Nepal, IELTS preparation classes Kohalpur, PTE training center Nepal, education consultancy Banke district, foreign education consultants Nepal, study visa guidance Nepal, university admission consultants Kohalpur" />
    <meta name="author" content="NextGen Hub Educational Consultancy" />
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="distribution" content="global" />
    <meta name="rating" content="general" />
    
    <!-- Geo-targeting for Local SEO -->
    <meta name="geo.region" content="NP-P5" />
    <meta name="geo.placename" content="Kohalpur, Banke, Nepal" />
    <meta name="geo.position" content="28.1833;81.6833" />
    <meta name="ICBM" content="28.1833, 81.6833" />
    
    <!-- Favicon -->
    <link rel="icon" href="/icon.png" type="image/x-icon" />
    <link rel="shortcut icon" href="/icon.png" type="image/x-icon" />
    <link rel="apple-touch-icon" href="/icon.png" />
    
    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="NextGen Hub - Best Study Abroad Consultants in Nepal | IELTS PTE Training" />
    <meta property="og:description" content="Leading study abroad consultancy in Kohalpur, Nepal. Expert IELTS/PTE training, visa processing, university admissions for Australia, Canada, UK, US, Germany." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nextgenhub.com.np" />
    <meta property="og:image" content="https://nextgenhub.com.np/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="NextGen Hub Educational Consultancy" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:locale:alternate" content="ne_NP" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@nextgenhub" />
    <meta name="twitter:creator" content="@nextgenhub" />
    <meta name="twitter:title" content="NextGen Hub - Best Study Abroad Consultants in Nepal" />
    <meta name="twitter:description" content="Leading study abroad consultancy in Kohalpur, Nepal. Expert IELTS/PTE training, visa processing, university admissions." />
    <meta name="twitter:image" content="https://nextgenhub.com.np/twitter-image.jpg" />
    
    <!-- Local Business Schema.org Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "EducationalOrganization",
      "name": "NextGen Hub Educational Consultancy",
      "alternateName": "NextGen Hub",
      "description": "Leading study abroad consultancy providing IELTS/PTE training, visa processing, and university admission services in Nepal",
      "url": "https://nextgenhub.com.np",
      "logo": "https://nextgenhub.com.np/logo.png",
      "image": "https://nextgenhub.com.np/og-image.jpg",
      "telephone": "+977-XXX-XXXXXXX",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Main Road",
        "addressLocality": "Kohalpur",
        "addressRegion": "Banke",
        "postalCode": "21900",
        "addressCountry": "NP"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "28.1833",
        "longitude": "81.6833"
      },
      "areaServed": [
        {
          "@type": "Place",
          "name": "Nepal"
        },
        {
          "@type": "Place",
          "name": "Banke District"
        },
        {
          "@type": "Place",
          "name": "Kohalpur"
        }
      ],
      "serviceType": [
        "Study Abroad Consultancy",
        "IELTS Training",
        "PTE Coaching",
        "Visa Processing",
        "University Admission Guidance"
      ],
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Educational Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "IELTS Training",
              "description": "Comprehensive IELTS preparation classes with expert trainers"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "PTE Coaching",
              "description": "Professional PTE coaching with practice tests and mock exams"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Student Visa Processing",
              "description": "End-to-end visa processing for Australia, Canada, UK, US, Germany"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "University Admission Guidance",
              "description": "Expert guidance for university applications and admissions"
            }
          }
        ]
      },
      "sameAs": [
        "https://www.facebook.com/nextgenhub",
        "https://www.instagram.com/nextgenhub",
        "https://www.linkedin.com/company/nextgenhub",
        "https://www.youtube.com/c/nextgenhub"
      ],
      "foundingDate": "2020",
      "founders": [
        {
          "@type": "Person",
          "name": "Founder Name"
        }
      ],
      "numberOfEmployees": "10-50",
      "slogan": "Your Gateway to Global Education"
    }
    </script>
    
    <!-- Service Schema for Study Abroad Consultancy -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": "Study Abroad Consultancy Services",
      "description": "Comprehensive study abroad consultancy including IELTS/PTE training, visa processing, and university admission guidance",
      "provider": {
        "@type": "Organization",
        "name": "NextGen Hub",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Main Road",
          "addressLocality": "Kohalpur",
          "addressRegion": "Banke",
          "addressCountry": "NP"
        }
      },
      "areaServed": "Nepal",
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Study Abroad Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Australia Student Visa",
              "description": "Complete visa processing for Australian universities"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Canada Study Permit",
              "description": "Study permit processing for Canadian institutions"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "UK Student Visa",
              "description": "UK Tier 4 student visa processing and guidance"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "US F-1 Visa",
              "description": "F-1 student visa processing for US universities"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Germany Student Visa",
              "description": "Student visa processing for German universities"
            }
          }
        ]
      }
    }
    </script>
    
    <!-- Breadcrumb Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://nextgenhub.com.np"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Services",
          "item": "https://nextgenhub.com.np/services"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "IELTS Training",
          "item": "https://nextgenhub.com.np/ielts-training"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "PTE Coaching",
          "item": "https://nextgenhub.com.np/pte-coaching"
        },
        {
          "@type": "ListItem",
          "position": 5,
          "name": "Visa Services",
          "item": "https://nextgenhub.com.np/visa-services"
        }
      ]
    }
    </script>
    
    <!-- Additional SEO Tags -->
    <link rel="canonical" href="https://nextgenhub.com.np" />
    <meta name="theme-color" content="#1e40af" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="NextGen Hub" />
    
    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Hreflang for International SEO -->
    <link rel="alternate" hreflang="en" href="https://nextgenhub.com.np" />
    <link rel="alternate" hreflang="ne" href="https://nextgenhub.com.np/ne" />
    <link rel="alternate" hreflang="x-default" href="https://nextgenhub.com.np" />
    
    <!-- DNS Prefetch for Performance -->
    <link rel="dns-prefetch" href="//www.google-analytics.com" />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    
  </head>

  <body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>
    
    <!-- Header with structured data -->
    <header role="banner">
      <nav role="navigation" aria-label="Main navigation">
        <!-- Navigation will be populated by React -->
      </nav>
    </header>
    
    <!-- Main content area -->
    <main id="main-content" role="main">
      <div id="root"></div>
    </main>
    
    <!-- Footer -->
    <footer role="contentinfo">
      <!-- Footer content will be populated by React -->
    </footer>
    
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Google Analytics (replace with your actual GA4 ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    
    <!-- Google Tag Manager (replace with your actual GTM ID) -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-XXXXXXX');</script>
    
    <!-- No Script Tag Manager -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXXX"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    
  </body>
</html>